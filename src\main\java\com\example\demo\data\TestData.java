package com.example.demo.data;

import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试数据
 * 用于创建测试规则和测试数据
 */
public class TestData {

    /**
     * 创建测试规则
     * 规则适用于上层，数据来自下层
     */
    public static List<AreaSplitStaticRuleDTO> createTestRules() {
        List<AreaSplitStaticRuleDTO> rules = new ArrayList<>();
        
        // 规则1: 总部级规则 - 适用于全国所有机构
        rules.add(createRule("HQ", "CHINA", "HQ_TARGET", "1.0000"));
        
        // 规则2: 大区级规则 - 适用于北部大区所有机构
        rules.add(createRule("NORTH", "NORTH_AREA", "NORTH_TARGET", "1.0000"));
        
        // 规则3: 省级规则 - 适用于北京省级分部所有机构
        rules.add(createRule("BJ_PROV", "BEIJING", "BJ_PROV_TARGET", "1.0000"));
        
        // 规则4: 市级规则 - 适用于北京市分行所有机构
        rules.add(createRule("BJ_CITY", "BEIJING", "BJ_CITY_TARGET", "1.0000"));
        
        // 规则5: 区级规则 - 适用于北京东城支行所有机构
        rules.add(createRule("BJ_EAST", "DONGCHENG", "BJ_EAST_TARGET", "1.0000"));
        
        // 规则6: 网点级规则 - 适用于北京东城支行网点1
        rules.add(createRule("BJ_EAST_01", "DONGCHENG", "BJ_EAST_01_TARGET", "1.0000"));
        
        return rules;
    }
    
    /**
     * 创建测试数据
     * 数据来自最低级别
     */
    public static List<ShrAreaSplitDTO> createTestDTOs() {
        List<ShrAreaSplitDTO> dtos = new ArrayList<>();
        
        // DTO1: 网点级数据 - 北京东城支行网点1
        dtos.add(createDTO("BJ_EAST_01", "DONGCHENG"));
        
        // DTO2: 区级数据 - 北京东城支行
        dtos.add(createDTO("BJ_EAST", "DONGCHENG"));
        
        // DTO3: 市级数据 - 北京市分行
        dtos.add(createDTO("BJ_CITY", "BEIJING"));
        
        // DTO4: 省级数据 - 北京省级分部
        dtos.add(createDTO("BJ_PROV", "BEIJING"));
        
        // DTO5: 大区级数据 - 北部大区
        dtos.add(createDTO("NORTH", "NORTH_AREA"));
        
        // DTO6: 总部级数据 - 总部
        dtos.add(createDTO("HQ", "CHINA"));
        
        return dtos;
    }
    
    /**
     * 创建规则
     */
    private static AreaSplitStaticRuleDTO createRule(String agency, String region, String targetAgency, String ratio) {
        AreaSplitStaticRuleDTO rule = new AreaSplitStaticRuleDTO();
        rule.setSkAgency(agency);
        rule.setSkRegion(region);
        rule.setSkAccount("-1");
        rule.setSkAccountType("-1");
        rule.setSkInvpty("-1");
        rule.setSkInvptyType("-1");
        rule.setSkProduct("-1");
        rule.setSkProductType("-1");
        rule.setDkAgencyType("*");
        rule.setDkCustType("*");
        rule.setSkTradeaccoReg("-1");
        rule.setAgencyno("*");
        rule.setNetno("*");
        rule.setDkShareType("*");
        rule.setCserialno("*");
        rule.setDkTano("*");
        rule.setSkAccountOfAg("-1");
        rule.setDkBourseflag("*");
        rule.setEffectiveFrom("********");
        rule.setEffectiveTo("********");
        rule.setRatio(ratio);
        rule.setTargetAgency(targetAgency);
        rule.setTargetRegion(region);
        return rule;
    }
    
    /**
     * 创建DTO
     */
    private static ShrAreaSplitDTO createDTO(String agency, String region) {
        ShrAreaSplitDTO dto = new ShrAreaSplitDTO();
        dto.setSkAgency(agency);
        dto.setSkRegion(region);
        dto.setSkAccount("ACC001");
        dto.setSkAccountType("1");
        dto.setSkInvPty("INV001");
        dto.setSkInvPtyType("1");
        dto.setSkProduct("PROD001");
        dto.setSkProdType("1");
        dto.setDkAgencyType("1");
        dto.setDkCustType("1");
        dto.setSkTradeAccoReg("REG001");
        dto.setAgencyNo("AG001");
        dto.setNetNo("NET001");
        dto.setDkShareType("1");
        dto.setTrdShrTrxSerialNo("TRX001");
        dto.setDkTano("TANO001");
        dto.setSkAccountOfAg("ACAG001");
        dto.setDkBourseFlag("1");
        dto.setEffectiveFrom("********");
        dto.setEffectiveTo("********");
        return dto;
    }
}
