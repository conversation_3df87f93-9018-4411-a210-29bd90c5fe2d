package com.example.demo.advice;

import com.example.demo.model.RequestWithUser;
import com.example.demo.service.CurrentUserService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * AspectJ 切面：拦截对 userId 字段的读取，首次注入并缓存
 */
@Aspect
@Component
public class UserIdFieldAspect {

    @Autowired
    private CurrentUserService currentUserService;

    // 捕获对任意 com.example.demo.model 包下 userId 字段的读取
    @Pointcut("get(Long com.example.demo.model..*.userId)")
    public void getUserIdField() {}

    @Around("getUserIdField() && this(req)")
    public Object aroundFieldGet(ProceedingJoinPoint pjp, RequestWithUser req) throws Throwable {
        // 读取字段值
        Long id = (Long) pjp.proceed();
        if (id == null) {
            id = currentUserService.getCurrentUserId();
            req.setUserId(id);
        }
        return id;
    }
}
