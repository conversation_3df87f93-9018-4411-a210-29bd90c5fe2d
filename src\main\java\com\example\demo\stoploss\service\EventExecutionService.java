package com.example.demo.stoploss.service;

import com.example.demo.stoploss.handler.EventHandler;
import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.StopLossEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 事件执行服务
 * 负责执行止损事件
 */
@Service
public class EventExecutionService {
    
    private final Map<StopLossEvent.EventType, EventHandler> eventHandlers;
    
    @Autowired
    public EventExecutionService(List<EventHandler> handlers) {
        this.eventHandlers = new HashMap<>();
        
        // 注册所有事件处理器
        for (EventHandler handler : handlers) {
            registerHandler(handler);
        }
    }
    
    /**
     * 注册事件处理器
     */
    private void registerHandler(EventHandler handler) {
        StopLossEvent.EventType type = handler.getSupportedEventType();
        eventHandlers.put(type, handler);
        
        // 对于支持多种类型的处理器，需要特殊处理
        if (handler.getClass().getSimpleName().equals("NotificationEventHandler")) {
            eventHandlers.put(StopLossEvent.EventType.SMS_NOTIFICATION, handler);
            eventHandlers.put(StopLossEvent.EventType.SYSTEM_NOTIFICATION, handler);
        } else if (handler.getClass().getSimpleName().equals("TradingEventHandler")) {
            eventHandlers.put(StopLossEvent.EventType.REDUCE_POSITION, handler);
            eventHandlers.put(StopLossEvent.EventType.CLOSE_POSITION, handler);
            eventHandlers.put(StopLossEvent.EventType.HEDGE_POSITION, handler);
            eventHandlers.put(StopLossEvent.EventType.RESUME_TRADING, handler);
        }
    }
    
    /**
     * 执行单个事件
     */
    public boolean executeEvent(StopLossEvent event, MonitoringContext context) {
        if (event == null || !event.isEnabled()) {
            return false;
        }
        
        EventHandler handler = eventHandlers.get(event.getEventType());
        if (handler == null) {
            System.err.println("未找到事件类型 " + event.getEventType() + " 的处理器");
            return false;
        }
        
        try {
            // 执行前预处理
            if (!handler.preExecute(event, context)) {
                System.out.println("事件 " + event.getEventId() + " 预处理失败，跳过执行");
                return false;
            }
            
            // 执行事件
            boolean success = handler.execute(event, context);
            
            // 执行后后处理
            handler.postExecute(event, context, success);
            
            return success;
        } catch (Exception e) {
            System.err.println("事件执行失败: " + e.getMessage());
            handler.postExecute(event, context, false);
            return false;
        }
    }
    
    /**
     * 异步执行单个事件
     */
    @Async
    public CompletableFuture<Boolean> executeEventAsync(StopLossEvent event, MonitoringContext context) {
        boolean result = executeEvent(event, context);
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 执行事件列表
     */
    public Map<String, Boolean> executeEvents(List<StopLossEvent> events, MonitoringContext context) {
        Map<String, Boolean> results = new HashMap<>();
        
        if (events == null || events.isEmpty()) {
            return results;
        }
        
        // 获取启用的事件并按优先级排序
        List<StopLossEvent> enabledEvents = events.stream()
                .filter(StopLossEvent::isEnabled)
                .sorted(Comparator.comparingInt(StopLossEvent::getPriority))
                .toList();
        
        for (StopLossEvent event : enabledEvents) {
            boolean success;
            
            if (event.isAsync() && supportsAsync(event)) {
                // 异步执行
                executeEventAsync(event, context);
                success = true; // 异步执行默认返回true
            } else {
                // 同步执行
                success = executeEvent(event, context);
            }
            
            results.put(event.getEventId(), success);
            
            // 如果是关键事件执行失败，可以选择停止后续事件执行
            if (!success && isKeyEvent(event)) {
                System.err.println("关键事件 " + event.getEventId() + " 执行失败，停止后续事件执行");
                break;
            }
        }
        
        return results;
    }
    
    /**
     * 检查事件是否支持异步执行
     */
    private boolean supportsAsync(StopLossEvent event) {
        EventHandler handler = eventHandlers.get(event.getEventType());
        return handler != null && handler.supportsAsync();
    }
    
    /**
     * 检查是否为关键事件
     */
    private boolean isKeyEvent(StopLossEvent event) {
        // 优先级为0的事件被认为是关键事件
        return event.getPriority() == 0;
    }
    
    /**
     * 批量异步执行事件
     */
    @Async
    public CompletableFuture<Map<String, Boolean>> executeEventsAsync(List<StopLossEvent> events, 
                                                                     MonitoringContext context) {
        Map<String, Boolean> results = executeEvents(events, context);
        return CompletableFuture.completedFuture(results);
    }
    
    /**
     * 检查事件是否已执行
     */
    public boolean isEventExecuted(String eventId, MonitoringContext context) {
        return context.isEventExecuted(eventId);
    }
    
    /**
     * 获取事件执行历史
     */
    public Map<String, java.time.LocalDateTime> getEventExecutionHistory(MonitoringContext context) {
        return new HashMap<>(context.getEventExecutionHistory());
    }
    
    /**
     * 清除事件执行历史
     */
    public void clearEventExecutionHistory(MonitoringContext context) {
        context.clearEventExecutionHistory();
    }
    
    /**
     * 获取已注册的事件处理器信息
     */
    public Map<StopLossEvent.EventType, String> getRegisteredHandlers() {
        Map<StopLossEvent.EventType, String> handlerInfo = new HashMap<>();
        
        for (Map.Entry<StopLossEvent.EventType, EventHandler> entry : eventHandlers.entrySet()) {
            handlerInfo.put(entry.getKey(), entry.getValue().getHandlerDescription());
        }
        
        return handlerInfo;
    }
    
    /**
     * 验证事件配置
     */
    public boolean validateEvent(StopLossEvent event) {
        if (event == null) {
            return false;
        }
        
        EventHandler handler = eventHandlers.get(event.getEventType());
        return handler != null && handler.validateEvent(event);
    }
    
    /**
     * 验证事件列表配置
     */
    public Map<String, String> validateEvents(List<StopLossEvent> events) {
        Map<String, String> validationResults = new HashMap<>();
        
        if (events == null) {
            return validationResults;
        }
        
        for (StopLossEvent event : events) {
            if (!validateEvent(event)) {
                validationResults.put(event.getEventId(), "事件配置无效");
            } else {
                validationResults.put(event.getEventId(), "配置有效");
            }
        }
        
        return validationResults;
    }
}
