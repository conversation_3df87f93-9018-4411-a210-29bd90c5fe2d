package com.example.demo;

import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.model.ShrAreaSplitResult;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 性能测试类 - 使用main方法直接测试getCurrentShareSplitResult方法的性能
 */
public class PerformanceTest {

    // 机构层级关系
    private static Map<String, ArrayList<String>> agency_hierarchy = new HashMap<>();
    // 区域层级关系
    private static Map<String, ArrayList<String>> region_hierarchy = new HashMap<>();
    // 区域拆分静态规则
    private static List<AreaSplitStaticRuleDTO> areaSplitStaticRule = new ArrayList<>();

    public static void main(String[] args) throws Exception {
        // 默认测试20万条规则
        int ruleCount = 200000;
        
        // 如果有命令行参数，使用参数指定的规则数量
        if (args.length > 0) {
            try {
                ruleCount = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.out.println("无效的规则数量参数，使用默认值: " + ruleCount);
            }
        }
        
        System.out.println("开始性能测试，规则数量: " + ruleCount);
        
        // 初始化测试数据
        initHierarchyData();
        generateRandomRules(ruleCount);
        
        // 创建测试DTO
        ShrAreaSplitDTO testDTO = createTestDTO();
        
        // 使用反射调用私有方法
        long startTime = System.currentTimeMillis();
        ArrayList<ShrAreaSplitResult> results = invokeGetCurrentShareSplitResult(testDTO);
        long endTime = System.currentTimeMillis();
        
        // 输出结果
        long executionTime = endTime - startTime;
        System.out.println("测试完成，执行时间: " + executionTime + " 毫秒");
        System.out.println("结果数量: " + results.size());
    }
    
    /**
     * 使用反射调用私有方法getCurrentShareSplitResult
     */
    @SuppressWarnings("unchecked")
    private static ArrayList<ShrAreaSplitResult> invokeGetCurrentShareSplitResult(ShrAreaSplitDTO dto) throws Exception {
        // 创建一个临时类，包含getCurrentShareSplitResult方法的实现
        TestServiceImpl testService = new TestServiceImpl();
        
        // 设置必要的字段
        testService.setAgencyHierarchy(agency_hierarchy);
        testService.setRegionHierarchy(region_hierarchy);
        testService.setAreaSplitStaticRule(areaSplitStaticRule);
        
        // 调用方法
        return testService.getCurrentShareSplitResult(dto);
    }
    
    /**
     * 生成随机测试规则
     */
    private static void generateRandomRules(int count) {
        System.out.println("生成" + count + "条随机规则...");
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < count; i++) {
            areaSplitStaticRule.add(generateRandomRule());
        }
        
        long endTime = System.currentTimeMillis();
        System.out.println("规则生成完成，耗时: " + (endTime - startTime) + " 毫秒");
    }
    
    /**
     * 生成随机的区域拆分规则
     */
    private static AreaSplitStaticRuleDTO generateRandomRule() {
        AreaSplitStaticRuleDTO rule = new AreaSplitStaticRuleDTO();
        
        // 随机生成机构和区域
        String[] agencies = {"BJ", "SH", "GZ", "SZ", "-1"};
        String[] regions = {"BJ", "SH", "GZ", "SZ", "-1"};
        String[] accounts = {"ACC001", "ACC002", "ACC003", "-1"};
        String[] types = {"1", "2", "3", "-1"};
        String[] wildcards = {"*"};
        
        // 随机选择值
        int agencyIndex = (int) (Math.random() * agencies.length);
        int regionIndex = (int) (Math.random() * regions.length);
        int accountIndex = (int) (Math.random() * accounts.length);
        int typeIndex = (int) (Math.random() * types.length);
        
        rule.setSkAgency(agencies[agencyIndex]);
        rule.setSkRegion(regions[regionIndex]);
        rule.setSkAccount(accounts[accountIndex]);
        rule.setSkAccountType(types[typeIndex]);
        rule.setSkInvpty(accounts[(int) (Math.random() * accounts.length)]);
        rule.setSkInvptyType(types[(int) (Math.random() * types.length)]);
        rule.setSkProduct(accounts[(int) (Math.random() * accounts.length)]);
        rule.setSkProductType(types[(int) (Math.random() * types.length)]);
        rule.setDkAgencyType(wildcards[0]);
        rule.setDkCustType(wildcards[0]);
        rule.setSkTradeaccoReg(accounts[(int) (Math.random() * accounts.length)]);
        rule.setAgencyno(wildcards[0]);
        rule.setNetno(wildcards[0]);
        rule.setDkShareType(wildcards[0]);
        rule.setCserialno(wildcards[0]);
        rule.setDkTano(wildcards[0]);
        rule.setSkAccountOfAg(accounts[(int) (Math.random() * accounts.length)]);
        rule.setDkBourseflag(wildcards[0]);
        
        // 设置有效期
        int year = 2023;
        int startMonth = (int) (Math.random() * 12) + 1;
        int endMonth = startMonth + (int) (Math.random() * (12 - startMonth + 1));
        String startDate = String.format("%d%02d01", year, startMonth);
        String endDate = String.format("%d%02d28", year, endMonth);
        rule.setEffectiveFrom(startDate);
        rule.setEffectiveTo(endDate);
        
        // 设置比例
        double ratio = Math.random() * 0.5; // 0到0.5之间的随机比例
        rule.setRatio(String.format("%.4f", ratio));
        
        // 设置目标机构和区域
        rule.setTargetAgency(agencies[agencyIndex] + "001");
        rule.setTargetRegion(regions[regionIndex]);
        
        return rule;
    }
    
    /**
     * 创建测试DTO
     */
    private static ShrAreaSplitDTO createTestDTO() {
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");
        return testDTO;
    }
    
    /**
     * 初始化层级关系数据
     */
    private static void initHierarchyData() {
        // 清空现有数据
        agency_hierarchy.clear();
        region_hierarchy.clear();
        
        // 初始化机构层级关系
        ArrayList<String> bj_agencies = new ArrayList<>();
        bj_agencies.add("BJ001");
        bj_agencies.add("BJ002");
        agency_hierarchy.put("BJ", bj_agencies);
        
        ArrayList<String> sh_agencies = new ArrayList<>();
        sh_agencies.add("SH001");
        sh_agencies.add("SH002");
        agency_hierarchy.put("SH", sh_agencies);
        
        ArrayList<String> gz_agencies = new ArrayList<>();
        gz_agencies.add("GZ001");
        gz_agencies.add("GZ002");
        agency_hierarchy.put("GZ", gz_agencies);
        
        ArrayList<String> sz_agencies = new ArrayList<>();
        sz_agencies.add("SZ001");
        sz_agencies.add("SZ002");
        agency_hierarchy.put("SZ", sz_agencies);
        
        // 初始化区域层级关系
        ArrayList<String> north_regions = new ArrayList<>();
        north_regions.add("BJ");
        north_regions.add("TJ");
        region_hierarchy.put("NORTH", north_regions);
        
        ArrayList<String> east_regions = new ArrayList<>();
        east_regions.add("SH");
        east_regions.add("JS");
        region_hierarchy.put("EAST", east_regions);
        
        ArrayList<String> south_regions = new ArrayList<>();
        south_regions.add("GZ");
        south_regions.add("SZ");
        region_hierarchy.put("SOUTH", south_regions);
    }
    
    /**
     * 内部类，实现TestService中的getCurrentShareSplitResult方法
     */
    private static class TestServiceImpl {
        private Map<String, ArrayList<String>> agency_hierarchy;
        private Map<String, ArrayList<String>> region_hierarchy;
        private List<AreaSplitStaticRuleDTO> areaSplitStaticRule;
        
        public void setAgencyHierarchy(Map<String, ArrayList<String>> agency_hierarchy) {
            this.agency_hierarchy = agency_hierarchy;
        }
        
        public void setRegionHierarchy(Map<String, ArrayList<String>> region_hierarchy) {
            this.region_hierarchy = region_hierarchy;
        }
        
        public void setAreaSplitStaticRule(List<AreaSplitStaticRuleDTO> areaSplitStaticRule) {
            this.areaSplitStaticRule = areaSplitStaticRule;
        }
        
        /**
         * 获取当前分享拆分结果
         */
        public ArrayList<ShrAreaSplitResult> getCurrentShareSplitResult(ShrAreaSplitDTO shrAreaSplitDTO) {
            ArrayList<ShrAreaSplitResult> result = new ArrayList<>();
            ArrayList<String> sk_agencyList = new ArrayList<String>();
            ArrayList<String> sk_regionList = new ArrayList<String>();
            if (null != agency_hierarchy.get(shrAreaSplitDTO.getSkAgency()))
                sk_agencyList = agency_hierarchy.get(shrAreaSplitDTO.getSkAgency());
            if (null != region_hierarchy.get(shrAreaSplitDTO.getSkRegion()))
                sk_regionList = region_hierarchy.get(shrAreaSplitDTO.getSkRegion());
            BigDecimal ratio = new BigDecimal("1.0000");
            List<String[]> timeSlice = new ArrayList<>();//待拆分的数据
            String[] data = {shrAreaSplitDTO.getEffectiveFrom(), shrAreaSplitDTO.getEffectiveTo()};  //保有数据的持有时间
            timeSlice.add(data);
            for (AreaSplitStaticRuleDTO ruleDTO : areaSplitStaticRule) {
                if (!"-1".equals(ruleDTO.getSkAgency()) && !sk_agencyList.contains(ruleDTO.getSkAgency())) continue;
                if (!"-1".equals(ruleDTO.getSkRegion()) && !sk_regionList.contains(ruleDTO.getSkRegion())) continue;
                if (!"-1".equals(ruleDTO.getSkAccount()) && !(ruleDTO.getSkAccount() == shrAreaSplitDTO.getSkAccount() || ruleDTO.getSkAccount().equals(shrAreaSplitDTO.getSkAccount())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkAccountType()) && !(ruleDTO.getSkAccountType() == shrAreaSplitDTO.getSkAccountType() || ruleDTO.getSkAccountType().equals(shrAreaSplitDTO.getSkAccountType())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkInvpty()) && !(ruleDTO.getSkInvpty() == shrAreaSplitDTO.getSkInvPty() || ruleDTO.getSkInvpty().equals(shrAreaSplitDTO.getSkInvPty())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkInvptyType()) && !(ruleDTO.getSkInvptyType() == shrAreaSplitDTO.getSkInvPtyType() || ruleDTO.getSkInvptyType().equals(shrAreaSplitDTO.getSkInvPtyType())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkProduct()) && !(ruleDTO.getSkProduct() == shrAreaSplitDTO.getSkProduct() || ruleDTO.getSkProduct().equals(shrAreaSplitDTO.getSkProduct())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkProductType()) && !(ruleDTO.getSkProductType() == shrAreaSplitDTO.getSkProdType() || ruleDTO.getSkProductType().equals(shrAreaSplitDTO.getSkProdType())))
                    continue;
                if (!"*".equals(ruleDTO.getDkAgencyType()) && !(ruleDTO.getDkAgencyType() == shrAreaSplitDTO.getDkAgencyType() || ruleDTO.getDkAgencyType().equals(shrAreaSplitDTO.getDkAgencyType())))
                    continue;
                if (!"*".equals(ruleDTO.getDkCustType()) && !(ruleDTO.getDkCustType() == shrAreaSplitDTO.getDkCustType() || ruleDTO.getDkCustType().equals(shrAreaSplitDTO.getDkCustType())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkTradeaccoReg()) && !(ruleDTO.getSkTradeaccoReg() == shrAreaSplitDTO.getSkTradeAccoReg() || ruleDTO.getSkTradeaccoReg().equals(shrAreaSplitDTO.getSkTradeAccoReg())))
                    continue;
                if (!"*".equals(ruleDTO.getAgencyno()) && !(ruleDTO.getAgencyno() == shrAreaSplitDTO.getAgencyNo() || ruleDTO.getAgencyno().equals(shrAreaSplitDTO.getAgencyNo())))
                    continue;
                if (!"*".equals(ruleDTO.getNetno()) && !(ruleDTO.getNetno() == shrAreaSplitDTO.getNetNo() || ruleDTO.getNetno().equals(shrAreaSplitDTO.getNetNo())))
                    continue;
                if (!"*".equals(ruleDTO.getDkShareType()) && !(ruleDTO.getDkShareType() == shrAreaSplitDTO.getDkShareType() || ruleDTO.getDkShareType().equals(shrAreaSplitDTO.getDkShareType())))
                    continue;
                if (!"*".equals(ruleDTO.getCserialno()) && !(ruleDTO.getCserialno() == shrAreaSplitDTO.getTrdShrTrxSerialNo() || ruleDTO.getCserialno().equals(shrAreaSplitDTO.getTrdShrTrxSerialNo())))
                    continue;
                if (!"*".equals(ruleDTO.getDkTano()) && !(ruleDTO.getDkTano() == shrAreaSplitDTO.getDkTano() || ruleDTO.getDkTano().equals(shrAreaSplitDTO.getDkTano())))
                    continue;
                if (!"-1".equals(ruleDTO.getSkAccountOfAg()) && !(ruleDTO.getSkAccountOfAg() == shrAreaSplitDTO.getSkAccountOfAg() || !ruleDTO.getSkAccountOfAg().equals(shrAreaSplitDTO.getSkAccountOfAg())))
                    continue;
                if (!"*".equals(ruleDTO.getDkBourseflag()) && !(ruleDTO.getDkBourseflag() == shrAreaSplitDTO.getDkBourseFlag() || ruleDTO.getDkBourseflag().equals(shrAreaSplitDTO.getDkBourseFlag())))
                    continue;
                String[] current = new String[2]; //当前拆分数据
                List<String[]> effective_data = new ArrayList<String[]>(timeSlice);
                List<String[]> currents = new ArrayList<String[]>(); //是否一条策略适用在多个时间段上
                for (int i = 0; i < timeSlice.size(); i++) {
                    String[] resource = timeSlice.get(i);
                    int data_from = Integer.parseInt(resource[0]);
                    int data_to = Integer.parseInt(resource[1]);
                    int rule_from = Integer.parseInt(ruleDTO.getEffectiveFrom());
                    int rule_to = Integer.parseInt(ruleDTO.getEffectiveTo());
                    boolean flag = false;  //数据的起止日是否命中规则起始日
                    if (rule_from <= data_from && data_from < rule_to && rule_to < data_to) {
                        flag = true;
                        String[] first = new String[]{rule_to + "", data_to + ""};
                        current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        effective_data.add(first);
                    } else if (data_from <= rule_from && rule_to <= data_to && (data_from != rule_from || rule_to != data_to)) {
                        flag = true;
                        String[] first = new String[]{data_from + "", rule_from + ""};
                        String[] second = new String[]{rule_to + "", data_to + ""};
                        current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        if (data_from != rule_from) effective_data.add(first);
                        if (rule_to != data_to) effective_data.add(second);
                    } else if (data_from < rule_from && rule_from < data_to && data_to <= rule_to) {
                        flag = true;
                        String[] first = new String[]{data_from + "", rule_from + ""};
                        current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        effective_data.add(first);
                    } else if (rule_from <= data_from && data_to <= rule_to) {
                        flag = true;
                        current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        String[] finalCurrent = current;
                        if(currents.stream().noneMatch(e -> java.util.Arrays.equals(e, finalCurrent))){
                            currents.add(current);
                        }
                    }
                    //情况1：啥都没命中，继续遍历其他规则，同时拆出一条起止日都为null的数据（所以规则排序影响生成起止日为null的数据条数，不过null数据不影响最终结果）
                    //情况2：命中了，且新规则权重是1，拆出新数据，timeSlice里老时间段删掉，得到新时间段（也有可能timeSlice已经空了）
                    //情况3：命中了，权重不为1，拆出新数据，timeSlice里新老时间段都有,ratio剩余值等下条策略减少到0
                    //注意：情况3要求策略组id相同的策略紧挨着依次处理，确保ratio能减到0，而不是被其他时间段策略减为负数
                    if (ratio.subtract(new BigDecimal(ruleDTO.getRatio())).toString().equals("0.0000") && flag) {
                        effective_data.remove(resource);
                    }
                }
                timeSlice = effective_data;
                ratio = ratio.subtract(new BigDecimal(ruleDTO.getRatio()));
                if(currents.size()>1){
                    //一条策略适用在多个时间段上，需要存多段拆分结果
                    for (int i = 0; i < currents.size(); i++) {
                        fillShrAreaSplitResult(result,shrAreaSplitDTO,ruleDTO,currents.get(i));
                    }
                }else{
                    fillShrAreaSplitResult(result,shrAreaSplitDTO,ruleDTO,current);
                }
                if (timeSlice.size() == 0) {
                    return result;
                }
                if (ratio.toString().equals("0.0000")) {
                    ratio = new BigDecimal("1.0000");
                }
            }
            return result;
        }
        
        private String[] currentTimeSlice(int dataFrom, int dataTo, int ruleFrom, int ruleTo) {
            String[] result = new String[2];
            if (dataFrom >= ruleFrom) {
                if (dataTo < ruleTo) {
                    result[0] = dataFrom + "";
                    result[1] = dataTo + "";
                } else {
                    result[0] = dataFrom + "";
                    result[1] = ruleTo + "";
                }
            } else {
                if (dataTo < ruleTo) {
                    result[0] = ruleFrom + "";
                    result[1] = dataTo + "";
                } else {
                    result[0] = ruleFrom + "";
                    result[1] = ruleTo + "";
                }
            }
            return result;
        }
        
        private void fillShrAreaSplitResult(ArrayList<ShrAreaSplitResult> result, ShrAreaSplitDTO shrAreaSplitDTO, 
                                          AreaSplitStaticRuleDTO ruleDTO, String[] current) {
            ShrAreaSplitResult splitResult = new ShrAreaSplitResult();
            
            // 复制原始数据属性
            splitResult.setSkAgency(shrAreaSplitDTO.getSkAgency());
            splitResult.setSkRegion(shrAreaSplitDTO.getSkRegion());
            splitResult.setSkAccount(shrAreaSplitDTO.getSkAccount());
            splitResult.setSkAccountType(shrAreaSplitDTO.getSkAccountType());
            splitResult.setSkInvPty(shrAreaSplitDTO.getSkInvPty());
            splitResult.setSkInvPtyType(shrAreaSplitDTO.getSkInvPtyType());
            splitResult.setSkProduct(shrAreaSplitDTO.getSkProduct());
            splitResult.setSkProdType(shrAreaSplitDTO.getSkProdType());
            splitResult.setDkAgencyType(shrAreaSplitDTO.getDkAgencyType());
            splitResult.setDkCustType(shrAreaSplitDTO.getDkCustType());
            splitResult.setSkTradeAccoReg(shrAreaSplitDTO.getSkTradeAccoReg());
            splitResult.setAgencyNo(shrAreaSplitDTO.getAgencyNo());
            splitResult.setNetNo(shrAreaSplitDTO.getNetNo());
            splitResult.setDkShareType(shrAreaSplitDTO.getDkShareType());
            splitResult.setTrdShrTrxSerialNo(shrAreaSplitDTO.getTrdShrTrxSerialNo());
            splitResult.setDkTano(shrAreaSplitDTO.getDkTano());
            splitResult.setSkAccountOfAg(shrAreaSplitDTO.getSkAccountOfAg());
            splitResult.setDkBourseFlag(shrAreaSplitDTO.getDkBourseFlag());
            
            // 设置拆分结果特有属性
            splitResult.setEffectiveFrom(current[0]);
            splitResult.setEffectiveTo(current[1]);
            splitResult.setRatio(ruleDTO.getRatio());
            splitResult.setTargetAgency(ruleDTO.getTargetAgency());
            splitResult.setTargetRegion(ruleDTO.getTargetRegion());
            
            result.add(splitResult);
        }
    }
}
