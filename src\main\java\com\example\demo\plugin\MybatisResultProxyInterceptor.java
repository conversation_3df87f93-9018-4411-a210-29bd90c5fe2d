package com.example.demo.plugin;

import com.example.demo.model.RequestWithUser;
import com.example.demo.service.CurrentUserService;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.aop.framework.ProxyFactory;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Properties;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.reflect.Method;

/**
 * MyBatis 插件：为结果对象动态代理，添加 RequestWithUser 接口，实现 userId 属性的懒加载与缓存
 */
@Component
@Intercepts({
    @Signature(
        type = ResultSetHandler.class,
        method = "handleResultSets",
        args = {Statement.class}
    )
})
public class MybatisResultProxyInterceptor implements Interceptor {

    @Autowired
    private CurrentUserService currentUserService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object resultObj = invocation.proceed();
        if (resultObj instanceof List) {
            List<?> list = (List<?>) resultObj;
            List<Object> proxiedList = new ArrayList<>(list.size());
            for (Object element : list) {
                proxiedList.add(wrap(element));
            }
            return proxiedList;
        }
        return wrap(resultObj);
    }

    private Object wrap(Object target) {
        if (target == null) {
            return null;
        }
        // 仅为实现 RequestWithUser 接口的结果对象创建代理，延迟获取 userId
        if (!(target instanceof RequestWithUser)) {
            return target;
        }
        RequestWithUser entity = (RequestWithUser) target;
        ProxyFactory factory = new ProxyFactory(entity);
        factory.setProxyTargetClass(true);
        Map<String, Object> cache = new ConcurrentHashMap<>();
        factory.addAdvice((MethodInterceptor) invocation -> {
            Method method = invocation.getMethod();
            String name = method.getName();
            if ("getUserId".equals(name)) {
                return cache.computeIfAbsent("userId", k -> currentUserService.getCurrentUserId());
            } else if ("setUserId".equals(name)) {
                cache.put("userId", invocation.getArguments()[0]);
                return null;
            }
            return invocation.proceed();
        });
        return factory.getProxy();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // no-op
    }
}
