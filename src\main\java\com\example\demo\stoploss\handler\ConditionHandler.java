package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.StopLossCondition;

/**
 * 条件处理器接口
 * 定义条件评估的标准接口
 */
public interface ConditionHandler {
    
    /**
     * 评估条件是否满足
     * 
     * @param condition 要评估的条件
     * @param context 监控上下文
     * @return 条件是否满足
     */
    boolean evaluate(StopLossCondition condition, MonitoringContext context);
    
    /**
     * 获取处理器支持的条件类型
     * 
     * @return 支持的条件类型
     */
    StopLossCondition.ConditionType getSupportedConditionType();
    
    /**
     * 验证条件配置是否有效
     * 
     * @param condition 要验证的条件
     * @return 验证结果，true表示有效
     */
    default boolean validateCondition(StopLossCondition condition) {
        return condition != null && 
               condition.getConditionType() == getSupportedConditionType() &&
               condition.isEnabled();
    }
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取处理器描述
     * 
     * @return 处理器描述
     */
    default String getHandlerDescription() {
        return "条件处理器：" + getSupportedConditionType().getDescription();
    }
}
