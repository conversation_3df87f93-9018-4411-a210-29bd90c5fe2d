Write-Host "Testing Stop Loss API..." -ForegroundColor Green

# Test 1: Get engine status
Write-Host "1. Getting engine status..." -ForegroundColor Yellow
$response = Invoke-WebRequest -Uri "http://localhost:8080/api/stoploss/monitoring/engine/status" -Method GET
Write-Host "Status: $($response.StatusCode)" -ForegroundColor Cyan
Write-Host "Content: $($response.Content)" -ForegroundColor White

# Test 2: Get all rules
Write-Host "`n2. Getting all rules..." -ForegroundColor Yellow
$response = Invoke-WebRequest -Uri "http://localhost:8080/api/stoploss/rules" -Method GET
Write-Host "Status: $($response.StatusCode)" -ForegroundColor Cyan
Write-Host "Rules count: $(($response.Content | ConvertFrom-Json).Count)" -ForegroundColor White

Write-Host "`nAPI Test completed!" -ForegroundColor Green
