package com.example.demo.model;

import lombok.Data;

@Data
public class AreaSplitStaticRuleDTO {
    private String skAgency;
    private String skRegion;
    private String skAccount;
    private String skAccountType;
    private String skInvpty;
    private String skInvptyType;
    private String skProduct;
    private String skProductType;
    private String dkAgencyType;
    private String dkCustType;
    private String skTradeaccoReg;
    private String agencyno;
    private String netno;
    private String dkShareType;
    private String cserialno;
    private String dkTano;
    private String skAccountOfAg;
    private String dkBourseflag;
    private String effectiveFrom;
    private String effectiveTo;
    private String ratio;
    private String targetAgency;
    private String targetRegion;
}
