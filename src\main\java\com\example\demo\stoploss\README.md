# 养老金组合止损监控引擎

## 概述

这是一个灵活的养老金组合止损监控引擎，支持多种条件组合和事件处理，具备止损和恢复功能。

## 核心特性

### 1. 灵活的条件引擎
- **价格条件**: 净值下跌、日收益率亏损、累计收益率亏损、最大回撤
- **成交量条件**: 成交量异常、成交量萎缩
- **时间条件**: 时间窗口、交易时间
- **逻辑组合**: 支持AND、OR、自定义逻辑

### 2. 可扩展的事件处理
- **通知事件**: 邮件通知、短信通知、系统通知
- **交易事件**: 停止交易、减仓、平仓、对冲、恢复交易
- **风控事件**: 风险预警、冻结账户、限制交易
- **日志事件**: 记录日志、审计日志

### 3. 止损恢复机制
- 支持配置恢复条件
- 自动监控恢复条件
- 触发恢复事件

### 4. 实时监控
- 定时监控任务
- 手动触发检查
- 监控状态管理

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controller    │    │     Service     │    │     Handler     │
│                 │    │                 │    │                 │
│ StopLossController │──│ MonitoringService │──│ ConditionHandler│
│                 │    │                 │    │                 │
│                 │    │ ConfigService   │    │ EventHandler    │
│                 │    │                 │    │                 │
│                 │    │ EvaluationService│    │                 │
│                 │    │                 │    │                 │
│                 │    │ ExecutionService│    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Model       │    │     Config      │    │     Demo        │
│                 │    │                 │    │                 │
│ PensionPortfolio│    │ StopLossConfig  │    │ MonitoringDemo  │
│ StopLossRule    │    │                 │    │                 │
│ StopLossCondition│   │                 │    │                 │
│ StopLossEvent   │    │                 │    │                 │
│ MonitoringContext│   │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 使用示例

### 1. 创建组合
```java
PensionPortfolio portfolio = new PensionPortfolio();
portfolio.setPortfolioId("PENSION_001");
portfolio.setPortfolioName("稳健型养老金组合");
portfolio.setCurrentNetValue(BigDecimal.valueOf(1.0500));
portfolio.setDailyReturn(BigDecimal.valueOf(-0.0094));
```

### 2. 配置止损条件
```java
// 日收益率条件
StopLossCondition condition = StopLossCondition.createDailyReturnLossCondition(
    "DAILY_RETURN_CONDITION", BigDecimal.valueOf(-1.0));

// 最大回撤条件
StopLossCondition drawdownCondition = StopLossCondition.createMaxDrawdownCondition(
    "MAX_DRAWDOWN_CONDITION", BigDecimal.valueOf(5.0));
```

### 3. 配置事件
```java
// 邮件通知事件
StopLossEvent emailEvent = StopLossEvent.createEmailNotificationEvent(
    "EMAIL_ALERT", "<EMAIL>", 
    "止损预警 - {portfolioName}", 
    "组合{portfolioId}触发止损条件");

// 停止交易事件
StopLossEvent stopEvent = StopLossEvent.createStopTradingEvent(
    "STOP_TRADING", "PENSION_001");
```

### 4. 创建规则
```java
StopLossRule rule = StopLossRule.createSimpleStopLossRule(
    "RULE_001", "日收益率止损规则",
    Arrays.asList("PENSION_001"),
    Arrays.asList(condition),
    Arrays.asList(emailEvent, stopEvent));
```

### 5. 启动监控
```java
// 添加规则
ruleConfigurationService.addRule(rule);

// 启动监控
monitoringService.startMonitoring(portfolio);
```

## REST API

### 监控管理
- `POST /api/stoploss/monitoring/start` - 启动监控
- `POST /api/stoploss/monitoring/stop/{portfolioId}` - 停止监控
- `POST /api/stoploss/monitoring/pause/{portfolioId}` - 暂停监控
- `POST /api/stoploss/monitoring/resume/{portfolioId}` - 恢复监控
- `GET /api/stoploss/monitoring/status/{portfolioId}` - 获取监控状态

### 规则管理
- `POST /api/stoploss/rules` - 添加规则
- `PUT /api/stoploss/rules/{ruleId}` - 更新规则
- `DELETE /api/stoploss/rules/{ruleId}` - 删除规则
- `GET /api/stoploss/rules` - 获取所有规则
- `GET /api/stoploss/rules/{ruleId}` - 获取指定规则

### 组合管理
- `PUT /api/stoploss/portfolio/{portfolioId}` - 更新组合数据
- `POST /api/stoploss/monitoring/check/{portfolioId}` - 手动触发检查

## 扩展开发

### 1. 自定义条件处理器
```java
@Component
public class CustomConditionHandler implements ConditionHandler {
    @Override
    public boolean evaluate(StopLossCondition condition, MonitoringContext context) {
        // 实现自定义条件逻辑
        return false;
    }
    
    @Override
    public StopLossCondition.ConditionType getSupportedConditionType() {
        return StopLossCondition.ConditionType.CUSTOM;
    }
}
```

### 2. 自定义事件处理器
```java
@Component
public class CustomEventHandler implements EventHandler {
    @Override
    public boolean execute(StopLossEvent event, MonitoringContext context) {
        // 实现自定义事件逻辑
        return true;
    }
    
    @Override
    public StopLossEvent.EventType getSupportedEventType() {
        return StopLossEvent.EventType.CUSTOM;
    }
}
```

## 配置说明

### 监控频率
默认每5秒执行一次监控检查，可在`StopLossMonitoringService`中修改：
```java
@Scheduled(fixedRate = 5000) // 修改这里的值
```

### 异步执行
事件可以配置为异步执行，在`StopLossConfig`中配置线程池参数。

### 日志级别
可以通过修改各个Handler中的日志输出来调整日志级别。

## 注意事项

1. **数据一致性**: 确保组合数据的及时更新
2. **性能考虑**: 大量组合监控时注意性能优化
3. **异常处理**: 条件评估和事件执行都有异常处理机制
4. **扩展性**: 通过实现Handler接口可以轻松扩展功能
5. **配置验证**: 规则配置会进行有效性验证

## 运行演示

启动Spring Boot应用后，`StopLossMonitoringDemo`会自动运行演示程序，展示完整的监控流程。
