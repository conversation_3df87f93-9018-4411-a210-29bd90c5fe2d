package com.example.demo.stoploss.service;

import com.example.demo.stoploss.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 止损监控服务
 * 核心监控引擎，负责协调条件评估和事件执行
 */
@Service
public class StopLossMonitoringService {
    
    @Autowired
    private ConditionEvaluationService conditionEvaluationService;
    
    @Autowired
    private EventExecutionService eventExecutionService;
    
    @Autowired
    private RuleConfigurationService ruleConfigurationService;
    
    // 监控上下文缓存
    private final Map<String, MonitoringContext> monitoringContexts = new ConcurrentHashMap<>();
    
    // 监控状态
    private volatile boolean monitoringEnabled = true;
    
    /**
     * 启动监控
     */
    public void startMonitoring(PensionPortfolio portfolio) {
        String contextId = generateContextId(portfolio.getPortfolioId());
        MonitoringContext context = new MonitoringContext(contextId, portfolio);
        monitoringContexts.put(contextId, context);
        
        System.out.println("开始监控组合: " + portfolio.getPortfolioId());
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring(String portfolioId) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        if (context != null) {
            context.setStatus(MonitoringContext.MonitoringStatus.STOPPED);
            monitoringContexts.remove(contextId);
            System.out.println("停止监控组合: " + portfolioId);
        }
    }
    
    /**
     * 暂停监控
     */
    public void pauseMonitoring(String portfolioId) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        if (context != null) {
            context.setStatus(MonitoringContext.MonitoringStatus.PAUSED);
            System.out.println("暂停监控组合: " + portfolioId);
        }
    }
    
    /**
     * 恢复监控
     */
    public void resumeMonitoring(String portfolioId) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        if (context != null) {
            context.setStatus(MonitoringContext.MonitoringStatus.ACTIVE);
            System.out.println("恢复监控组合: " + portfolioId);
        }
    }
    
    /**
     * 更新组合数据
     */
    public void updatePortfolioData(String portfolioId, PensionPortfolio updatedPortfolio) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        if (context != null) {
            context.setPortfolio(updatedPortfolio);
            context.updateLastCheckTime();
        }
    }
    
    /**
     * 定时监控任务
     */
    @Scheduled(fixedRate = 5000) // 每5秒执行一次
    public void scheduledMonitoring() {
        if (!monitoringEnabled) {
            return;
        }
        
        for (MonitoringContext context : monitoringContexts.values()) {
            if (context.isActive()) {
                try {
                    performMonitoring(context);
                } catch (Exception e) {
                    System.err.println("监控执行失败: " + e.getMessage());
                    context.setError("监控执行失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 执行监控逻辑
     */
    private void performMonitoring(MonitoringContext context) {
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio == null) {
            return;
        }
        
        // 获取适用的规则
        List<StopLossRule> applicableRules = ruleConfigurationService.getApplicableRules(portfolio.getPortfolioId());
        
        for (StopLossRule rule : applicableRules) {
            context.setCurrentRule(rule);
            
            // 根据组合状态决定检查止损还是恢复条件
            if (portfolio.isInStopLoss()) {
                // 已触发止损，检查恢复条件
                if (rule.isRecoveryEnabled()) {
                    checkRecoveryConditions(rule, context);
                }
            } else {
                // 正常状态，检查止损条件
                checkStopLossConditions(rule, context);
            }
        }
        
        context.updateLastCheckTime();
    }
    
    /**
     * 检查止损条件
     */
    private void checkStopLossConditions(StopLossRule rule, MonitoringContext context) {
        List<StopLossCondition> conditions = rule.getEnabledConditions();
        if (conditions.isEmpty()) {
            return;
        }
        
        boolean conditionsMet = conditionEvaluationService.evaluateConditions(
                conditions, rule.getConditionLogic(), context);
        
        if (conditionsMet) {
            System.out.println("止损条件满足，触发止损事件 - 规则: " + rule.getRuleId());
            
            // 执行止损事件
            List<StopLossEvent> events = rule.getEnabledEvents();
            Map<String, Boolean> results = eventExecutionService.executeEvents(events, context);
            
            // 记录执行结果
            logEventResults("止损事件", results);
        }
    }
    
    /**
     * 检查恢复条件
     */
    private void checkRecoveryConditions(StopLossRule rule, MonitoringContext context) {
        List<StopLossCondition> recoveryConditions = rule.getEnabledRecoveryConditions();
        if (recoveryConditions.isEmpty()) {
            return;
        }
        
        boolean conditionsMet = conditionEvaluationService.evaluateConditions(
                recoveryConditions, rule.getRecoveryConditionLogic(), context);
        
        if (conditionsMet) {
            System.out.println("恢复条件满足，触发恢复事件 - 规则: " + rule.getRuleId());
            
            // 执行恢复事件
            List<StopLossEvent> recoveryEvents = rule.getEnabledRecoveryEvents();
            Map<String, Boolean> results = eventExecutionService.executeEvents(recoveryEvents, context);
            
            // 记录执行结果
            logEventResults("恢复事件", results);
        }
    }
    
    /**
     * 记录事件执行结果
     */
    private void logEventResults(String eventType, Map<String, Boolean> results) {
        System.out.println("=== " + eventType + "执行结果 ===");
        for (Map.Entry<String, Boolean> entry : results.entrySet()) {
            System.out.println("事件 " + entry.getKey() + ": " + (entry.getValue() ? "成功" : "失败"));
        }
        System.out.println("========================");
    }
    
    /**
     * 手动触发监控检查
     */
    public void triggerManualCheck(String portfolioId) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        if (context != null && context.isActive()) {
            performMonitoring(context);
        }
    }
    
    /**
     * 获取监控状态
     */
    public Map<String, Object> getMonitoringStatus(String portfolioId) {
        String contextId = generateContextId(portfolioId);
        MonitoringContext context = monitoringContexts.get(contextId);
        
        Map<String, Object> status = new HashMap<>();
        if (context != null) {
            status.put("contextId", context.getContextId());
            status.put("status", context.getStatus().getDescription());
            status.put("lastCheckTime", context.getLastCheckTime());
            status.put("errorMessage", context.getErrorMessage());
            status.put("conditionResults", context.getConditionResults());
            status.put("eventExecutionHistory", context.getEventExecutionHistory());
        } else {
            status.put("status", "未监控");
        }
        
        return status;
    }
    
    /**
     * 获取所有监控状态
     */
    public Map<String, Map<String, Object>> getAllMonitoringStatus() {
        Map<String, Map<String, Object>> allStatus = new HashMap<>();
        
        for (MonitoringContext context : monitoringContexts.values()) {
            String portfolioId = context.getPortfolio().getPortfolioId();
            allStatus.put(portfolioId, getMonitoringStatus(portfolioId));
        }
        
        return allStatus;
    }
    
    /**
     * 生成上下文ID
     */
    private String generateContextId(String portfolioId) {
        return "MONITOR_" + portfolioId + "_" + System.currentTimeMillis();
    }
    
    /**
     * 启用/禁用监控
     */
    public void setMonitoringEnabled(boolean enabled) {
        this.monitoringEnabled = enabled;
        System.out.println("监控引擎" + (enabled ? "已启用" : "已禁用"));
    }
    
    /**
     * 检查监控是否启用
     */
    public boolean isMonitoringEnabled() {
        return monitoringEnabled;
    }
}
