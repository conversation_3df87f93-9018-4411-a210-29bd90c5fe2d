package com.example.demo.data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 层级关系数据
 * 存储机构和区域的层级关系
 * key 是最低级别的节点，value 是包含该节点及其所有上级节点的列表
 */
public class HierarchyData {

    /**
     * 初始化机构层级关系
     * 例如：key=静安区，value=[中国, 上海, 静安区]
     */
    public static Map<String, ArrayList<String>> initAgencyHierarchy() {
        Map<String, ArrayList<String>> hierarchy = new HashMap<>();
        
        // 总部层级
        hierarchy.put("HQ", new ArrayList<>(Arrays.asList("HQ")));
        
        // 北部大区层级
        hierarchy.put("NORTH", new ArrayList<>(Arrays.asList("HQ", "NORTH")));
        
        // 东部大区层级
        hierarchy.put("EAST", new ArrayList<>(Arrays.asList("HQ", "EAST")));
        
        // 南部大区层级
        hierarchy.put("SOUTH", new ArrayList<>(Arrays.asList("HQ", "SOUTH")));
        
        // 北京省级分部层级
        hierarchy.put("BJ_PROV", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV")));
        
        // 上海省级分部层级
        hierarchy.put("SH_PROV", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV")));
        
        // 广东省级分部层级
        hierarchy.put("GD_PROV", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV")));
        
        // 北京市分行层级
        hierarchy.put("BJ_CITY", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV", "BJ_CITY")));
        
        // 上海市分行层级
        hierarchy.put("SH_CITY", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV", "SH_CITY")));
        
        // 广州市分行层级
        hierarchy.put("GZ_CITY", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "GZ_CITY")));
        
        // 深圳市分行层级
        hierarchy.put("SZ_CITY", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "SZ_CITY")));
        
        // 北京东城支行层级
        hierarchy.put("BJ_EAST", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV", "BJ_CITY", "BJ_EAST")));
        
        // 北京西城支行层级
        hierarchy.put("BJ_WEST", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV", "BJ_CITY", "BJ_WEST")));
        
        // 上海浦东支行层级
        hierarchy.put("SH_PUDONG", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV", "SH_CITY", "SH_PUDONG")));
        
        // 上海黄浦支行层级
        hierarchy.put("SH_HUANGPU", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV", "SH_CITY", "SH_HUANGPU")));
        
        // 广州天河支行层级
        hierarchy.put("GZ_TIANHE", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "GZ_CITY", "GZ_TIANHE")));
        
        // 广州越秀支行层级
        hierarchy.put("GZ_YUEXIU", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "GZ_CITY", "GZ_YUEXIU")));
        
        // 深圳福田支行层级
        hierarchy.put("SZ_FUTIAN", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "SZ_CITY", "SZ_FUTIAN")));
        
        // 深圳南山支行层级
        hierarchy.put("SZ_NANSHAN", new ArrayList<>(Arrays.asList("HQ", "SOUTH", "GD_PROV", "SZ_CITY", "SZ_NANSHAN")));
        
        // 北京东城支行网点1层级
        hierarchy.put("BJ_EAST_01", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV", "BJ_CITY", "BJ_EAST", "BJ_EAST_01")));
        
        // 北京东城支行网点2层级
        hierarchy.put("BJ_EAST_02", new ArrayList<>(Arrays.asList("HQ", "NORTH", "BJ_PROV", "BJ_CITY", "BJ_EAST", "BJ_EAST_02")));
        
        // 上海浦东支行网点1层级
        hierarchy.put("SH_PUDONG_01", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV", "SH_CITY", "SH_PUDONG", "SH_PUDONG_01")));
        
        // 上海浦东支行网点2层级
        hierarchy.put("SH_PUDONG_02", new ArrayList<>(Arrays.asList("HQ", "EAST", "SH_PROV", "SH_CITY", "SH_PUDONG", "SH_PUDONG_02")));
        
        return hierarchy;
    }
    
    /**
     * 初始化区域层级关系
     * 例如：key=静安区，value=[中国, 上海, 静安区]
     */
    public static Map<String, ArrayList<String>> initRegionHierarchy() {
        Map<String, ArrayList<String>> hierarchy = new HashMap<>();
        
        // 全国层级
        hierarchy.put("CHINA", new ArrayList<>(Arrays.asList("CHINA")));
        
        // 华北地区层级
        hierarchy.put("NORTH_AREA", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA")));
        
        // 华东地区层级
        hierarchy.put("EAST_AREA", new ArrayList<>(Arrays.asList("CHINA", "EAST_AREA")));
        
        // 华南地区层级
        hierarchy.put("SOUTH_AREA", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA")));
        
        // 北京市层级
        hierarchy.put("BEIJING", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA", "BEIJING")));
        
        // 天津市层级
        hierarchy.put("TIANJIN", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA", "TIANJIN")));
        
        // 上海市层级
        hierarchy.put("SHANGHAI", new ArrayList<>(Arrays.asList("CHINA", "EAST_AREA", "SHANGHAI")));
        
        // 江苏省层级
        hierarchy.put("JIANGSU", new ArrayList<>(Arrays.asList("CHINA", "EAST_AREA", "JIANGSU")));
        
        // 广东省层级
        hierarchy.put("GUANGDONG", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG")));
        
        // 福建省层级
        hierarchy.put("FUJIAN", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "FUJIAN")));
        
        // 北京东城区层级
        hierarchy.put("DONGCHENG", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA", "BEIJING", "DONGCHENG")));
        
        // 北京西城区层级
        hierarchy.put("XICHENG", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA", "BEIJING", "XICHENG")));
        
        // 北京朝阳区层级
        hierarchy.put("CHAOYANG", new ArrayList<>(Arrays.asList("CHINA", "NORTH_AREA", "BEIJING", "CHAOYANG")));
        
        // 上海浦东新区层级
        hierarchy.put("PUDONG", new ArrayList<>(Arrays.asList("CHINA", "EAST_AREA", "SHANGHAI", "PUDONG")));
        
        // 上海黄浦区层级
        hierarchy.put("HUANGPU", new ArrayList<>(Arrays.asList("CHINA", "EAST_AREA", "SHANGHAI", "HUANGPU")));
        
        // 广州市层级
        hierarchy.put("GUANGZHOU", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "GUANGZHOU")));
        
        // 深圳市层级
        hierarchy.put("SHENZHEN", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "SHENZHEN")));
        
        // 广州天河区层级
        hierarchy.put("TIANHE", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "GUANGZHOU", "TIANHE")));
        
        // 广州越秀区层级
        hierarchy.put("YUEXIU", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "GUANGZHOU", "YUEXIU")));
        
        // 深圳福田区层级
        hierarchy.put("FUTIAN", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "SHENZHEN", "FUTIAN")));
        
        // 深圳南山区层级
        hierarchy.put("NANSHAN", new ArrayList<>(Arrays.asList("CHINA", "SOUTH_AREA", "GUANGDONG", "SHENZHEN", "NANSHAN")));
        
        return hierarchy;
    }
}
