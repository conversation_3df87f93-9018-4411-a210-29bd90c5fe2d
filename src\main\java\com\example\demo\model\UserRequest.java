package com.example.demo.model;
import com.example.demo.model.RequestWithUser;

public class UserRequest implements RequestWithUser {
    private Long userId;
    private String data;

    @Override
    public Long getUserId() {
        return userId;
    }

    @Override
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
