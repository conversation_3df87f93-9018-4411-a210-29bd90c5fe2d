package com.example.demo.stoploss.service;

import com.example.demo.stoploss.model.StopLossRule;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 规则配置服务
 * 负责管理止损规则的配置和查询
 */
@Service
public class RuleConfigurationService {
    
    // 规则存储
    private final Map<String, StopLossRule> rules = new ConcurrentHashMap<>();
    
    // 组合规则映射缓存
    private final Map<String, List<String>> portfolioRuleMapping = new ConcurrentHashMap<>();
    
    /**
     * 添加规则
     */
    public void addRule(StopLossRule rule) {
        if (rule == null || rule.getRuleId() == null) {
            throw new IllegalArgumentException("规则或规则ID不能为空");
        }
        
        rule.setLastModifyTime(LocalDateTime.now());
        rules.put(rule.getRuleId(), rule);
        
        // 更新组合规则映射
        updatePortfolioRuleMapping(rule);
        
        System.out.println("添加规则: " + rule.getRuleId() + " - " + rule.getRuleName());
    }
    
    /**
     * 更新规则
     */
    public void updateRule(StopLossRule rule) {
        if (rule == null || rule.getRuleId() == null) {
            throw new IllegalArgumentException("规则或规则ID不能为空");
        }
        
        if (!rules.containsKey(rule.getRuleId())) {
            throw new IllegalArgumentException("规则不存在: " + rule.getRuleId());
        }
        
        rule.setLastModifyTime(LocalDateTime.now());
        rules.put(rule.getRuleId(), rule);
        
        // 更新组合规则映射
        updatePortfolioRuleMapping(rule);
        
        System.out.println("更新规则: " + rule.getRuleId() + " - " + rule.getRuleName());
    }
    
    /**
     * 删除规则
     */
    public void removeRule(String ruleId) {
        if (ruleId == null) {
            throw new IllegalArgumentException("规则ID不能为空");
        }
        
        StopLossRule removedRule = rules.remove(ruleId);
        if (removedRule != null) {
            // 清理组合规则映射
            clearPortfolioRuleMapping(removedRule);
            System.out.println("删除规则: " + ruleId);
        }
    }
    
    /**
     * 获取规则
     */
    public StopLossRule getRule(String ruleId) {
        return rules.get(ruleId);
    }
    
    /**
     * 获取所有规则
     */
    public List<StopLossRule> getAllRules() {
        return new ArrayList<>(rules.values());
    }
    
    /**
     * 获取启用的规则
     */
    public List<StopLossRule> getEnabledRules() {
        return rules.values().stream()
                .filter(StopLossRule::isEnabled)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取适用于指定组合的规则
     */
    public List<StopLossRule> getApplicableRules(String portfolioId) {
        if (portfolioId == null) {
            return Collections.emptyList();
        }
        
        return rules.values().stream()
                .filter(rule -> rule.isEnabled() && rule.isApplicableToPortfolio(portfolioId))
                .sorted(Comparator.comparingInt(StopLossRule::getPriority))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据规则类型获取规则
     */
    public List<StopLossRule> getRulesByType(StopLossRule.RuleType ruleType) {
        return rules.values().stream()
                .filter(rule -> rule.getRuleType() == ruleType)
                .collect(Collectors.toList());
    }
    
    /**
     * 启用规则
     */
    public void enableRule(String ruleId) {
        StopLossRule rule = rules.get(ruleId);
        if (rule != null) {
            rule.setEnabled(true);
            rule.setLastModifyTime(LocalDateTime.now());
            System.out.println("启用规则: " + ruleId);
        }
    }
    
    /**
     * 禁用规则
     */
    public void disableRule(String ruleId) {
        StopLossRule rule = rules.get(ruleId);
        if (rule != null) {
            rule.setEnabled(false);
            rule.setLastModifyTime(LocalDateTime.now());
            System.out.println("禁用规则: " + ruleId);
        }
    }
    
    /**
     * 批量启用规则
     */
    public void enableRules(List<String> ruleIds) {
        for (String ruleId : ruleIds) {
            enableRule(ruleId);
        }
    }
    
    /**
     * 批量禁用规则
     */
    public void disableRules(List<String> ruleIds) {
        for (String ruleId : ruleIds) {
            disableRule(ruleId);
        }
    }
    
    /**
     * 验证规则配置
     */
    public Map<String, String> validateRule(StopLossRule rule) {
        Map<String, String> validationResults = new HashMap<>();
        
        if (rule == null) {
            validationResults.put("rule", "规则不能为空");
            return validationResults;
        }
        
        // 验证基本信息
        if (rule.getRuleId() == null || rule.getRuleId().trim().isEmpty()) {
            validationResults.put("ruleId", "规则ID不能为空");
        }
        
        if (rule.getRuleName() == null || rule.getRuleName().trim().isEmpty()) {
            validationResults.put("ruleName", "规则名称不能为空");
        }
        
        // 验证条件
        if (rule.getConditions() == null || rule.getConditions().isEmpty()) {
            validationResults.put("conditions", "至少需要一个条件");
        }
        
        // 验证事件
        if (rule.getEvents() == null || rule.getEvents().isEmpty()) {
            validationResults.put("events", "至少需要一个事件");
        }
        
        // 验证恢复配置
        if (rule.isRecoveryEnabled()) {
            if (rule.getRecoveryConditions() == null || rule.getRecoveryConditions().isEmpty()) {
                validationResults.put("recoveryConditions", "启用恢复功能时需要配置恢复条件");
            }
            if (rule.getRecoveryEvents() == null || rule.getRecoveryEvents().isEmpty()) {
                validationResults.put("recoveryEvents", "启用恢复功能时需要配置恢复事件");
            }
        }
        
        return validationResults;
    }
    
    /**
     * 更新组合规则映射
     */
    private void updatePortfolioRuleMapping(StopLossRule rule) {
        // 清理旧映射
        clearPortfolioRuleMapping(rule);
        
        // 添加新映射
        if (rule.getPortfolioIds() != null) {
            for (String portfolioId : rule.getPortfolioIds()) {
                portfolioRuleMapping.computeIfAbsent(portfolioId, k -> new ArrayList<>())
                        .add(rule.getRuleId());
            }
        }
    }
    
    /**
     * 清理组合规则映射
     */
    private void clearPortfolioRuleMapping(StopLossRule rule) {
        if (rule.getPortfolioIds() != null) {
            for (String portfolioId : rule.getPortfolioIds()) {
                List<String> ruleIds = portfolioRuleMapping.get(portfolioId);
                if (ruleIds != null) {
                    ruleIds.remove(rule.getRuleId());
                    if (ruleIds.isEmpty()) {
                        portfolioRuleMapping.remove(portfolioId);
                    }
                }
            }
        }
    }
    
    /**
     * 获取组合关联的规则ID列表
     */
    public List<String> getPortfolioRuleIds(String portfolioId) {
        return portfolioRuleMapping.getOrDefault(portfolioId, Collections.emptyList());
    }
    
    /**
     * 获取规则统计信息
     */
    public Map<String, Object> getRuleStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        long totalRules = rules.size();
        long enabledRules = rules.values().stream().filter(StopLossRule::isEnabled).count();
        long disabledRules = totalRules - enabledRules;
        
        Map<StopLossRule.RuleType, Long> ruleTypeCount = rules.values().stream()
                .collect(Collectors.groupingBy(StopLossRule::getRuleType, Collectors.counting()));
        
        statistics.put("totalRules", totalRules);
        statistics.put("enabledRules", enabledRules);
        statistics.put("disabledRules", disabledRules);
        statistics.put("ruleTypeCount", ruleTypeCount);
        statistics.put("portfolioCount", portfolioRuleMapping.size());
        
        return statistics;
    }
    
    /**
     * 清空所有规则
     */
    public void clearAllRules() {
        rules.clear();
        portfolioRuleMapping.clear();
        System.out.println("清空所有规则");
    }
    
    /**
     * 导出规则配置
     */
    public List<StopLossRule> exportRules() {
        return new ArrayList<>(rules.values());
    }
    
    /**
     * 导入规则配置
     */
    public void importRules(List<StopLossRule> rulesToImport) {
        for (StopLossRule rule : rulesToImport) {
            addRule(rule);
        }
        System.out.println("导入 " + rulesToImport.size() + " 个规则");
    }
}
