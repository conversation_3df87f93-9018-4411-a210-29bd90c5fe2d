package com.example.demo.controller;

import com.example.demo.model.UserRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @PostMapping("/info")
    public UserRequest info(@RequestBody UserRequest request) {
        // 演示调用 getUserId 和 data
        Long uid = request.getUserId();
        String data = request.getData();
        System.out.println("UserId: " + uid + ", Data: " + data);
        return request;
    }
}
