package com.example.demo;

import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.model.ShrAreaSplitResult;
import com.example.demo.service.TestService;

import java.util.ArrayList;
import java.util.List;

/**
 * 层级关系测试类
 * 用于测试复杂层级关系的处理
 */
public class HierarchyTest {

    public static void main(String[] args) {
        System.out.println("开始测试复杂层级关系处理...");
        
        // 创建TestService实例
        TestService testService = new TestService();
        
        // 执行层级关系测试
        testService.testHierarchyMatching();
        
        System.out.println("\n层级关系测试完成");
    }
}
