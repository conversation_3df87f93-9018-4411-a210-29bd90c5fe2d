package com.example.demo.stoploss.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 止损监控配置类
 * 配置异步执行和调度功能
 */
@Configuration
@EnableAsync
@EnableScheduling
public class StopLossConfig {
    
    /**
     * 配置异步执行器
     */
    @Bean(name = "stopLossTaskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("StopLoss-");
        executor.initialize();
        return executor;
    }
}
