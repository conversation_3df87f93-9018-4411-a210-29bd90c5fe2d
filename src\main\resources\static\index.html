<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域拆分性能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px 15px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>区域拆分性能测试</h1>
    
    <div class="card">
        <div class="form-group">
            <label for="ruleCount">规则数量:</label>
            <input type="number" id="ruleCount" value="200000" min="1" max="1000000">
        </div>
        <button id="runTest">运行测试</button>
        
        <div id="loading" class="loading">
            <p>测试运行中，请稍候...</p>
            <div class="spinner"></div>
            <p>生成大量规则并执行测试可能需要几分钟时间</p>
        </div>
        
        <div id="result" class="success">
            <h3>测试结果</h3>
            <p>规则数量: <span id="resultRuleCount"></span></p>
            <p>执行时间: <span id="resultTime"></span> 毫秒</p>
        </div>
    </div>
    
    <script>
        document.getElementById('runTest').addEventListener('click', function() {
            const ruleCount = document.getElementById('ruleCount').value;
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            // 显示加载中
            loading.style.display = 'block';
            result.style.display = 'none';
            
            // 调用API
            fetch(`/api/test/performance/${ruleCount}`)
                .then(response => response.json())
                .then(data => {
                    // 显示结果
                    document.getElementById('resultRuleCount').textContent = data.ruleCount;
                    document.getElementById('resultTime').textContent = data.executionTimeMs;
                    result.style.display = 'block';
                    loading.style.display = 'none';
                })
                .catch(error => {
                    alert('测试执行失败: ' + error);
                    loading.style.display = 'none';
                });
        });
    </script>
</body>
</html>
