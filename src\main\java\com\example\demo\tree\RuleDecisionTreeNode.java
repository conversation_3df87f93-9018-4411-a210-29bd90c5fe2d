package com.example.demo.tree;

import com.example.demo.data.HierarchyData;
import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.util.HierarchyMatcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 规则决策树节点
 */
public class RuleDecisionTreeNode {
    // 子节点映射
    private final Map<String, RuleDecisionTreeNode> children = new HashMap<>();
    // 匹配规则列表
    private final List<AreaSplitStaticRuleDTO> matchingRules = new ArrayList<>();
    // 通配符节点
    private RuleDecisionTreeNode wildcardNode;

    // 层级关系数据
    private static final Map<String, ArrayList<String>> agencyHierarchy = HierarchyData.initAgencyHierarchy();
    private static final Map<String, ArrayList<String>> regionHierarchy = HierarchyData.initRegionHierarchy();

    // 反向映射 - 存储每个数据值对应的规则值列表
    // 例如：数据机构="BJ_EAST_01" -> [规则机构="HQ", 规则机构="NORTH", ...]
    private Map<String, List<String>> agencyReverseMap;
    private Map<String, List<String>> regionReverseMap;

    // 当前属性名称
    private String attributeName;

    // 构造函数
    public RuleDecisionTreeNode(String attributeName) {
        this.attributeName = attributeName;

        // 初始化反向映射
        if ("skAgency".equals(attributeName)) {
            initAgencyReverseMap();
        } else if ("skRegion".equals(attributeName)) {
            initRegionReverseMap();
        }
    }

    // 初始化机构反向映射
    private void initAgencyReverseMap() {
        agencyReverseMap = new HashMap<>();

        // 遍历所有机构层级关系
        for (Map.Entry<String, ArrayList<String>> entry : agencyHierarchy.entrySet()) {
            String dataAgency = entry.getKey();
            ArrayList<String> hierarchy = entry.getValue();

            // 将数据机构映射到其所有上级机构
            agencyReverseMap.put(dataAgency, hierarchy);
        }
    }

    // 初始化区域反向映射
    private void initRegionReverseMap() {
        regionReverseMap = new HashMap<>();

        // 遍历所有区域层级关系
        for (Map.Entry<String, ArrayList<String>> entry : regionHierarchy.entrySet()) {
            String dataRegion = entry.getKey();
            ArrayList<String> hierarchy = entry.getValue();

            // 将数据区域映射到其所有上级区域
            regionReverseMap.put(dataRegion, hierarchy);
        }
    }

    // 添加规则
    public void addRule(AreaSplitStaticRuleDTO rule, List<String> attributeOrder, int depth) {
        if (depth >= attributeOrder.size()) {
            matchingRules.add(rule);
            return;
        }

        String currentAttribute = attributeOrder.get(depth);
        String attributeValue = getAttributeValue(rule, currentAttribute);

        // 处理通配符或默认值
        if ("-1".equals(attributeValue) || "*".equals(attributeValue)) {
            if (wildcardNode == null) {
                String nextAttribute = attributeOrder.get(depth + 1 < attributeOrder.size() ? depth + 1 : 0);
                wildcardNode = new RuleDecisionTreeNode(nextAttribute);
            }
            wildcardNode.addRule(rule, attributeOrder, depth + 1);
        } else {
            // 处理具体值
            String nextAttribute = attributeOrder.get(depth + 1 < attributeOrder.size() ? depth + 1 : 0);
            RuleDecisionTreeNode child = children.computeIfAbsent(attributeValue, k -> new RuleDecisionTreeNode(nextAttribute));
            child.addRule(rule, attributeOrder, depth + 1);
        }
    }

    // 查找匹配规则
    public List<AreaSplitStaticRuleDTO> findMatchingRules(ShrAreaSplitDTO dto, List<String> attributeOrder, int depth) {
        // 使用LinkedHashSet保持规则的原始顺序并去除重复
        Set<AreaSplitStaticRuleDTO> resultSet = new LinkedHashSet<>();

        // 收集匹配的规则
        collectMatchingRules(dto, attributeOrder, depth, resultSet);

        // 转换为List并返回
        return new ArrayList<>(resultSet);
    }

    // 收集匹配规则的辅助方法 - 使用反向映射优化
    private void collectMatchingRules(ShrAreaSplitDTO dto, List<String> attributeOrder, int depth, Set<AreaSplitStaticRuleDTO> resultSet) {
        // 先添加当前节点的规则，保持原始顺序
        resultSet.addAll(matchingRules);

        if (depth >= attributeOrder.size()) {
            return;
        }

        String currentAttribute = attributeOrder.get(depth);
        String attributeValue = getAttributeValue(dto, currentAttribute);

        // 判断当前属性是否需要层级匹配
        if ("skAgency".equals(currentAttribute)) {
            // 使用机构反向映射进行匹配
            if (agencyReverseMap != null) {
                List<String> matchingRuleValues = agencyReverseMap.get(attributeValue);
                if (matchingRuleValues != null) {
                    // 对每个匹配的规则值，继续递归匹配
                    for (String ruleValue : matchingRuleValues) {
                        RuleDecisionTreeNode childNode = children.get(ruleValue);
                        if (childNode != null) {
                            childNode.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
                        }
                    }
                }
            } else {
                // 如果反向映射不可用，回退到直接匹配
                RuleDecisionTreeNode matchingChild = children.get(attributeValue);
                if (matchingChild != null) {
                    matchingChild.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
                }
            }
        } else if ("skRegion".equals(currentAttribute)) {
            // 使用区域反向映射进行匹配
            if (regionReverseMap != null) {
                List<String> matchingRuleValues = regionReverseMap.get(attributeValue);
                if (matchingRuleValues != null) {
                    // 对每个匹配的规则值，继续递归匹配
                    for (String ruleValue : matchingRuleValues) {
                        RuleDecisionTreeNode childNode = children.get(ruleValue);
                        if (childNode != null) {
                            childNode.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
                        }
                    }
                }
            } else {
                // 如果反向映射不可用，回退到直接匹配
                RuleDecisionTreeNode matchingChild = children.get(attributeValue);
                if (matchingChild != null) {
                    matchingChild.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
                }
            }
        } else {
            // 对于不需要层级匹配的属性，使用精确匹配
            RuleDecisionTreeNode matchingChild = children.get(attributeValue);
            if (matchingChild != null) {
                matchingChild.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
            }
        }

        // 检查通配符匹配 - 后处理通配符，与原始方法一致
        if (wildcardNode != null) {
            wildcardNode.collectMatchingRules(dto, attributeOrder, depth + 1, resultSet);
        }
    }

    // 获取属性值 - 使用静态Map缓存属性获取方法
    private String getAttributeValue(Object obj, String attributeName) {
        // 根据对象类型和属性名获取属性值
        if (obj instanceof AreaSplitStaticRuleDTO) {
            AreaSplitStaticRuleDTO rule = (AreaSplitStaticRuleDTO) obj;
            // 使用更高效的switch语句
            return switch (attributeName) {
                case "skAgency" -> rule.getSkAgency();
                case "skRegion" -> rule.getSkRegion();
                case "skAccount" -> rule.getSkAccount();
                case "skAccountType" -> rule.getSkAccountType();
                case "skInvpty" -> rule.getSkInvpty();
                case "skInvptyType" -> rule.getSkInvptyType();
                case "skProduct" -> rule.getSkProduct();
                case "skProductType" -> rule.getSkProductType();
                case "dkAgencyType" -> rule.getDkAgencyType();
                case "dkCustType" -> rule.getDkCustType();
                case "skTradeaccoReg" -> rule.getSkTradeaccoReg();
                case "agencyno" -> rule.getAgencyno();
                case "netno" -> rule.getNetno();
                case "dkShareType" -> rule.getDkShareType();
                case "cserialno" -> rule.getCserialno();
                case "dkTano" -> rule.getDkTano();
                case "skAccountOfAg" -> rule.getSkAccountOfAg();
                case "dkBourseflag" -> rule.getDkBourseflag();
                default -> null;
            };
        } else if (obj instanceof ShrAreaSplitDTO) {
            ShrAreaSplitDTO dto = (ShrAreaSplitDTO) obj;
            return switch (attributeName) {
                case "skAgency" -> dto.getSkAgency();
                case "skRegion" -> dto.getSkRegion();
                case "skAccount" -> dto.getSkAccount();
                case "skAccountType" -> dto.getSkAccountType();
                case "skInvpty" -> dto.getSkInvPty();
                case "skInvptyType" -> dto.getSkInvPtyType();
                case "skProduct" -> dto.getSkProduct();
                case "skProductType" -> dto.getSkProdType();
                case "dkAgencyType" -> dto.getDkAgencyType();
                case "dkCustType" -> dto.getDkCustType();
                case "skTradeaccoReg" -> dto.getSkTradeAccoReg();
                case "agencyno" -> dto.getAgencyNo();
                case "netno" -> dto.getNetNo();
                case "dkShareType" -> dto.getDkShareType();
                case "cserialno" -> dto.getTrdShrTrxSerialNo();
                case "dkTano" -> dto.getDkTano();
                case "skAccountOfAg" -> dto.getSkAccountOfAg();
                case "dkBourseflag" -> dto.getDkBourseFlag();
                default -> null;
            };
        }
        return null;
    }

    // 只保留必要的getter方法
    public List<AreaSplitStaticRuleDTO> getMatchingRules() {
        return matchingRules;
    }
}
