package com.example.demo.stoploss.controller;

import com.example.demo.stoploss.model.PensionPortfolio;
import com.example.demo.stoploss.model.StopLossRule;
import com.example.demo.stoploss.service.RuleConfigurationService;
import com.example.demo.stoploss.service.StopLossMonitoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 止损监控控制器
 * 提供止损监控的REST API接口
 */
@RestController
@RequestMapping("/api/stoploss")
public class StopLossController {
    
    @Autowired
    private StopLossMonitoringService monitoringService;
    
    @Autowired
    private RuleConfigurationService ruleConfigurationService;
    
    /**
     * 启动监控
     */
    @PostMapping("/monitoring/start")
    public ResponseEntity<String> startMonitoring(@RequestBody PensionPortfolio portfolio) {
        try {
            monitoringService.startMonitoring(portfolio);
            return ResponseEntity.ok("监控已启动: " + portfolio.getPortfolioId());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("启动监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止监控
     */
    @PostMapping("/monitoring/stop/{portfolioId}")
    public ResponseEntity<String> stopMonitoring(@PathVariable String portfolioId) {
        try {
            monitoringService.stopMonitoring(portfolioId);
            return ResponseEntity.ok("监控已停止: " + portfolioId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("停止监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 暂停监控
     */
    @PostMapping("/monitoring/pause/{portfolioId}")
    public ResponseEntity<String> pauseMonitoring(@PathVariable String portfolioId) {
        try {
            monitoringService.pauseMonitoring(portfolioId);
            return ResponseEntity.ok("监控已暂停: " + portfolioId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("暂停监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 恢复监控
     */
    @PostMapping("/monitoring/resume/{portfolioId}")
    public ResponseEntity<String> resumeMonitoring(@PathVariable String portfolioId) {
        try {
            monitoringService.resumeMonitoring(portfolioId);
            return ResponseEntity.ok("监控已恢复: " + portfolioId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("恢复监控失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新组合数据
     */
    @PutMapping("/portfolio/{portfolioId}")
    public ResponseEntity<String> updatePortfolio(@PathVariable String portfolioId, 
                                                 @RequestBody PensionPortfolio portfolio) {
        try {
            monitoringService.updatePortfolioData(portfolioId, portfolio);
            return ResponseEntity.ok("组合数据已更新: " + portfolioId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("更新组合数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发监控检查
     */
    @PostMapping("/monitoring/check/{portfolioId}")
    public ResponseEntity<String> triggerCheck(@PathVariable String portfolioId) {
        try {
            monitoringService.triggerManualCheck(portfolioId);
            return ResponseEntity.ok("监控检查已触发: " + portfolioId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("触发监控检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控状态
     */
    @GetMapping("/monitoring/status/{portfolioId}")
    public ResponseEntity<Map<String, Object>> getMonitoringStatus(@PathVariable String portfolioId) {
        try {
            Map<String, Object> status = monitoringService.getMonitoringStatus(portfolioId);
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("error", "获取监控状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有监控状态
     */
    @GetMapping("/monitoring/status")
    public ResponseEntity<Map<String, Map<String, Object>>> getAllMonitoringStatus() {
        try {
            Map<String, Map<String, Object>> allStatus = monitoringService.getAllMonitoringStatus();
            return ResponseEntity.ok(allStatus);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("error", Map.of("error", "获取所有监控状态失败: " + e.getMessage())));
        }
    }
    
    /**
     * 添加规则
     */
    @PostMapping("/rules")
    public ResponseEntity<String> addRule(@RequestBody StopLossRule rule) {
        try {
            Map<String, String> validationResults = ruleConfigurationService.validateRule(rule);
            if (!validationResults.isEmpty()) {
                return ResponseEntity.badRequest().body("规则验证失败: " + validationResults);
            }
            
            ruleConfigurationService.addRule(rule);
            return ResponseEntity.ok("规则已添加: " + rule.getRuleId());
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("添加规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新规则
     */
    @PutMapping("/rules/{ruleId}")
    public ResponseEntity<String> updateRule(@PathVariable String ruleId, @RequestBody StopLossRule rule) {
        try {
            rule.setRuleId(ruleId);
            Map<String, String> validationResults = ruleConfigurationService.validateRule(rule);
            if (!validationResults.isEmpty()) {
                return ResponseEntity.badRequest().body("规则验证失败: " + validationResults);
            }
            
            ruleConfigurationService.updateRule(rule);
            return ResponseEntity.ok("规则已更新: " + ruleId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("更新规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除规则
     */
    @DeleteMapping("/rules/{ruleId}")
    public ResponseEntity<String> deleteRule(@PathVariable String ruleId) {
        try {
            ruleConfigurationService.removeRule(ruleId);
            return ResponseEntity.ok("规则已删除: " + ruleId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("删除规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取规则
     */
    @GetMapping("/rules/{ruleId}")
    public ResponseEntity<StopLossRule> getRule(@PathVariable String ruleId) {
        try {
            StopLossRule rule = ruleConfigurationService.getRule(ruleId);
            if (rule != null) {
                return ResponseEntity.ok(rule);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 获取所有规则
     */
    @GetMapping("/rules")
    public ResponseEntity<List<StopLossRule>> getAllRules() {
        try {
            List<StopLossRule> rules = ruleConfigurationService.getAllRules();
            return ResponseEntity.ok(rules);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 获取适用于指定组合的规则
     */
    @GetMapping("/rules/portfolio/{portfolioId}")
    public ResponseEntity<List<StopLossRule>> getApplicableRules(@PathVariable String portfolioId) {
        try {
            List<StopLossRule> rules = ruleConfigurationService.getApplicableRules(portfolioId);
            return ResponseEntity.ok(rules);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 启用规则
     */
    @PostMapping("/rules/{ruleId}/enable")
    public ResponseEntity<String> enableRule(@PathVariable String ruleId) {
        try {
            ruleConfigurationService.enableRule(ruleId);
            return ResponseEntity.ok("规则已启用: " + ruleId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("启用规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 禁用规则
     */
    @PostMapping("/rules/{ruleId}/disable")
    public ResponseEntity<String> disableRule(@PathVariable String ruleId) {
        try {
            ruleConfigurationService.disableRule(ruleId);
            return ResponseEntity.ok("规则已禁用: " + ruleId);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("禁用规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取规则统计信息
     */
    @GetMapping("/rules/statistics")
    public ResponseEntity<Map<String, Object>> getRuleStatistics() {
        try {
            Map<String, Object> statistics = ruleConfigurationService.getRuleStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("error", "获取规则统计失败: " + e.getMessage()));
        }
    }
    
    /**
     * 启用/禁用监控引擎
     */
    @PostMapping("/monitoring/engine/{enabled}")
    public ResponseEntity<String> setMonitoringEnabled(@PathVariable boolean enabled) {
        try {
            monitoringService.setMonitoringEnabled(enabled);
            return ResponseEntity.ok("监控引擎已" + (enabled ? "启用" : "禁用"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("设置监控引擎状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控引擎状态
     */
    @GetMapping("/monitoring/engine/status")
    public ResponseEntity<Map<String, Object>> getMonitoringEngineStatus() {
        try {
            Map<String, Object> status = Map.of(
                "enabled", monitoringService.isMonitoringEnabled(),
                "activeMonitoringCount", monitoringService.getAllMonitoringStatus().size()
            );
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("error", "获取监控引擎状态失败: " + e.getMessage()));
        }
    }
}
