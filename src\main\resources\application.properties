spring.application.name=demo

# Web Resources
spring.web.resources.static-locations=classpath:/static/

# Server Config
server.port=8080
server.tomcat.max-threads=200

# Logging Config
logging.level.root=INFO
logging.level.com.example.demo=DEBUG

# Database Config
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
