package com.example.demo.advice;

import com.example.demo.model.RequestWithUser;
import com.example.demo.service.CurrentUserService;
import org.aopalliance.intercept.MethodInterceptor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.aop.framework.ProxyFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class RequestWithUserArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private CurrentUserService currentUserService;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        boolean isTarget = RequestWithUser.class.isAssignableFrom(parameter.getParameterType());
        boolean isBody = parameter.hasParameterAnnotation(RequestBody.class);
        return isTarget && !isBody;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) throws Exception {
        Class<?> paramType = parameter.getParameterType();
        Object attr = BeanUtils.instantiateClass(paramType);
        WebDataBinder binder = binderFactory.createBinder(webRequest, attr, parameter.getParameterName());
        // 将请求参数转为 PropertyValues 绑定
        binder.bind(new MutablePropertyValues(webRequest.getParameterMap()));
        // 代理处理 getUserId 懒加载
        ProxyFactory factory = new ProxyFactory(attr);
        factory.setProxyTargetClass(true);
        factory.addAdvice((MethodInterceptor) invocation -> {
            String name = invocation.getMethod().getName();
            if ("getUserId".equals(name)) {
                RequestWithUser target = (RequestWithUser) attr;
                Long id = target.getUserId();
                if (id == null) {
                    id = currentUserService.getCurrentUserId();
                    target.setUserId(id);
                }
                return id;
            }
            return invocation.proceed();
        });
        return factory.getProxy();
    }
}
