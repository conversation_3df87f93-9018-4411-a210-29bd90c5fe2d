package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.PensionPortfolio;
import com.example.demo.stoploss.model.StopLossCondition;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 成交量相关条件处理器
 * 处理成交量异常、成交量萎缩等成交量相关条件
 */
@Component
public class VolumeConditionHandler implements ConditionHandler {
    
    @Override
    public boolean evaluate(StopLossCondition condition, MonitoringContext context) {
        if (!validateCondition(condition)) {
            return false;
        }
        
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio == null) {
            return false;
        }
        
        try {
            switch (condition.getConditionType()) {
                case VOLUME_SPIKE:
                    return evaluateVolumeSpike(condition, portfolio);
                case VOLUME_DROP:
                    return evaluateVolumeDrop(condition, portfolio);
                default:
                    return false;
            }
        } catch (Exception e) {
            context.setError("成交量条件评估失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public StopLossCondition.ConditionType getSupportedConditionType() {
        return StopLossCondition.ConditionType.VOLUME_SPIKE;
    }
    
    @Override
    public boolean validateCondition(StopLossCondition condition) {
        if (condition == null || !condition.isEnabled()) {
            return false;
        }
        
        StopLossCondition.ConditionType type = condition.getConditionType();
        return type == StopLossCondition.ConditionType.VOLUME_SPIKE ||
               type == StopLossCondition.ConditionType.VOLUME_DROP;
    }
    
    /**
     * 评估成交量异常条件
     */
    private boolean evaluateVolumeSpike(StopLossCondition condition, PensionPortfolio portfolio) {
        // 获取阈值倍数，默认为2倍
        BigDecimal thresholdMultiplier = condition.getDecimalParameter("thresholdMultiplier");
        if (thresholdMultiplier == null) {
            thresholdMultiplier = BigDecimal.valueOf(2.0);
        }
        
        Long currentVolume = portfolio.getCurrentVolume();
        Long averageVolume = portfolio.getAverageVolume();
        
        if (currentVolume == null || averageVolume == null || averageVolume == 0) {
            return false;
        }
        
        // 计算当前成交量与平均成交量的比率
        BigDecimal volumeRatio = BigDecimal.valueOf(currentVolume)
                .divide(BigDecimal.valueOf(averageVolume), 6, BigDecimal.ROUND_HALF_UP);
        
        // 检查是否超过阈值倍数
        return volumeRatio.compareTo(thresholdMultiplier) >= 0;
    }
    
    /**
     * 评估成交量萎缩条件
     */
    private boolean evaluateVolumeDrop(StopLossCondition condition, PensionPortfolio portfolio) {
        // 获取阈值比例，默认为0.5（即成交量低于平均值的50%）
        BigDecimal thresholdRatio = condition.getDecimalParameter("thresholdRatio");
        if (thresholdRatio == null) {
            thresholdRatio = BigDecimal.valueOf(0.5);
        }
        
        Long currentVolume = portfolio.getCurrentVolume();
        Long averageVolume = portfolio.getAverageVolume();
        
        if (currentVolume == null || averageVolume == null || averageVolume == 0) {
            return false;
        }
        
        // 计算当前成交量与平均成交量的比率
        BigDecimal volumeRatio = BigDecimal.valueOf(currentVolume)
                .divide(BigDecimal.valueOf(averageVolume), 6, BigDecimal.ROUND_HALF_UP);
        
        // 检查是否低于阈值比例
        return volumeRatio.compareTo(thresholdRatio) <= 0;
    }
    
    @Override
    public String getHandlerDescription() {
        return "成交量条件处理器：处理成交量异常、成交量萎缩等成交量相关条件";
    }
}
