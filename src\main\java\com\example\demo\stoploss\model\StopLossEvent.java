package com.example.demo.stoploss.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 止损事件定义
 * 定义当条件满足时需要执行的事件
 */
@Data
public class StopLossEvent {
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件名称
     */
    private String eventName;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 事件参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 执行优先级（数字越小优先级越高）
     */
    private int priority;
    
    /**
     * 是否启用
     */
    private boolean enabled;
    
    /**
     * 是否异步执行
     */
    private boolean async;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        // 通知类事件
        EMAIL_NOTIFICATION("邮件通知", "email_notification"),
        SMS_NOTIFICATION("短信通知", "sms_notification"),
        SYSTEM_NOTIFICATION("系统通知", "system_notification"),
        
        // 交易类事件
        STOP_TRADING("停止交易", "stop_trading"),
        REDUCE_POSITION("减仓", "reduce_position"),
        CLOSE_POSITION("平仓", "close_position"),
        HEDGE_POSITION("对冲", "hedge_position"),
        
        // 风控类事件
        RISK_ALERT("风险预警", "risk_alert"),
        FREEZE_ACCOUNT("冻结账户", "freeze_account"),
        LIMIT_TRADING("限制交易", "limit_trading"),
        
        // 日志类事件
        LOG_EVENT("记录日志", "log_event"),
        AUDIT_LOG("审计日志", "audit_log"),
        
        // 数据类事件
        UPDATE_STATUS("更新状态", "update_status"),
        SAVE_SNAPSHOT("保存快照", "save_snapshot"),
        
        // 恢复类事件
        RESUME_TRADING("恢复交易", "resume_trading"),
        UNFREEZE_ACCOUNT("解冻账户", "unfreeze_account"),
        CLEAR_ALERT("清除预警", "clear_alert"),
        
        // 自定义事件
        CUSTOM("自定义事件", "custom");
        
        private final String description;
        private final String code;
        
        EventType(String description, String code) {
            this.description = description;
            this.code = code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getCode() {
            return code;
        }
    }
    
    /**
     * 获取参数值
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }
    
    /**
     * 获取参数值（String类型）
     */
    public String getStringParameter(String key) {
        Object value = getParameter(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 设置参数值
     */
    public void setParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new java.util.HashMap<>();
        }
        parameters.put(key, value);
    }
    
    /**
     * 创建邮件通知事件
     */
    public static StopLossEvent createEmailNotificationEvent(String eventId, String recipient, String subject, String content) {
        StopLossEvent event = new StopLossEvent();
        event.setEventId(eventId);
        event.setEventName("邮件通知");
        event.setEventType(EventType.EMAIL_NOTIFICATION);
        event.setParameter("recipient", recipient);
        event.setParameter("subject", subject);
        event.setParameter("content", content);
        event.setPriority(1);
        event.setEnabled(true);
        event.setAsync(true);
        event.setDescription("发送邮件通知到" + recipient);
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
    
    /**
     * 创建停止交易事件
     */
    public static StopLossEvent createStopTradingEvent(String eventId, String portfolioId) {
        StopLossEvent event = new StopLossEvent();
        event.setEventId(eventId);
        event.setEventName("停止交易");
        event.setEventType(EventType.STOP_TRADING);
        event.setParameter("portfolioId", portfolioId);
        event.setPriority(0);
        event.setEnabled(true);
        event.setAsync(false);
        event.setDescription("停止组合" + portfolioId + "的交易");
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
    
    /**
     * 创建风险预警事件
     */
    public static StopLossEvent createRiskAlertEvent(String eventId, String portfolioId, String alertLevel) {
        StopLossEvent event = new StopLossEvent();
        event.setEventId(eventId);
        event.setEventName("风险预警");
        event.setEventType(EventType.RISK_ALERT);
        event.setParameter("portfolioId", portfolioId);
        event.setParameter("alertLevel", alertLevel);
        event.setPriority(1);
        event.setEnabled(true);
        event.setAsync(true);
        event.setDescription("组合" + portfolioId + "触发" + alertLevel + "级风险预警");
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
    
    /**
     * 创建日志记录事件
     */
    public static StopLossEvent createLogEvent(String eventId, String logLevel, String message) {
        StopLossEvent event = new StopLossEvent();
        event.setEventId(eventId);
        event.setEventName("记录日志");
        event.setEventType(EventType.LOG_EVENT);
        event.setParameter("logLevel", logLevel);
        event.setParameter("message", message);
        event.setPriority(9);
        event.setEnabled(true);
        event.setAsync(true);
        event.setDescription("记录" + logLevel + "级别日志");
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
    
    /**
     * 创建恢复交易事件
     */
    public static StopLossEvent createResumeTradingEvent(String eventId, String portfolioId) {
        StopLossEvent event = new StopLossEvent();
        event.setEventId(eventId);
        event.setEventName("恢复交易");
        event.setEventType(EventType.RESUME_TRADING);
        event.setParameter("portfolioId", portfolioId);
        event.setPriority(0);
        event.setEnabled(true);
        event.setAsync(false);
        event.setDescription("恢复组合" + portfolioId + "的交易");
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
}
