package com.example.demo.stoploss.service;

import com.example.demo.stoploss.handler.ConditionHandler;
import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.StopLossCondition;
import com.example.demo.stoploss.model.StopLossRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条件评估服务
 * 负责评估止损条件是否满足
 */
@Service
public class ConditionEvaluationService {
    
    private final Map<StopLossCondition.ConditionType, ConditionHandler> conditionHandlers;
    
    @Autowired
    public ConditionEvaluationService(List<ConditionHandler> handlers) {
        this.conditionHandlers = new HashMap<>();
        
        // 注册所有条件处理器
        for (ConditionHandler handler : handlers) {
            registerHandler(handler);
        }
    }
    
    /**
     * 注册条件处理器
     */
    private void registerHandler(ConditionHandler handler) {
        StopLossCondition.ConditionType type = handler.getSupportedConditionType();
        conditionHandlers.put(type, handler);
        
        // 对于支持多种类型的处理器，需要特殊处理
        if (handler.getClass().getSimpleName().equals("PriceConditionHandler")) {
            conditionHandlers.put(StopLossCondition.ConditionType.DAILY_RETURN_LOSS, handler);
            conditionHandlers.put(StopLossCondition.ConditionType.CUMULATIVE_RETURN_LOSS, handler);
            conditionHandlers.put(StopLossCondition.ConditionType.MAX_DRAWDOWN, handler);
        } else if (handler.getClass().getSimpleName().equals("VolumeConditionHandler")) {
            conditionHandlers.put(StopLossCondition.ConditionType.VOLUME_DROP, handler);
        } else if (handler.getClass().getSimpleName().equals("TimeConditionHandler")) {
            conditionHandlers.put(StopLossCondition.ConditionType.TRADING_HOURS, handler);
        }
    }
    
    /**
     * 评估单个条件
     */
    public boolean evaluateCondition(StopLossCondition condition, MonitoringContext context) {
        if (condition == null || !condition.isEnabled()) {
            return false;
        }
        
        ConditionHandler handler = conditionHandlers.get(condition.getConditionType());
        if (handler == null) {
            System.err.println("未找到条件类型 " + condition.getConditionType() + " 的处理器");
            return false;
        }
        
        try {
            boolean result = handler.evaluate(condition, context);
            
            // 缓存评估结果
            context.setConditionResult(condition.getConditionId(), result);
            
            return result;
        } catch (Exception e) {
            System.err.println("条件评估失败: " + e.getMessage());
            context.setError("条件评估失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 评估条件列表
     */
    public boolean evaluateConditions(List<StopLossCondition> conditions, 
                                    StopLossRule.ConditionLogic logic, 
                                    MonitoringContext context) {
        if (conditions == null || conditions.isEmpty()) {
            return false;
        }
        
        // 获取启用的条件
        List<StopLossCondition> enabledConditions = conditions.stream()
                .filter(StopLossCondition::isEnabled)
                .toList();
        
        if (enabledConditions.isEmpty()) {
            return false;
        }
        
        switch (logic) {
            case AND:
                return evaluateAndLogic(enabledConditions, context);
            case OR:
                return evaluateOrLogic(enabledConditions, context);
            case CUSTOM:
                return evaluateCustomLogic(enabledConditions, context);
            default:
                return false;
        }
    }
    
    /**
     * 评估AND逻辑（所有条件都必须满足）
     */
    private boolean evaluateAndLogic(List<StopLossCondition> conditions, MonitoringContext context) {
        for (StopLossCondition condition : conditions) {
            if (!evaluateCondition(condition, context)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 评估OR逻辑（任一条件满足即可）
     */
    private boolean evaluateOrLogic(List<StopLossCondition> conditions, MonitoringContext context) {
        for (StopLossCondition condition : conditions) {
            if (evaluateCondition(condition, context)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 评估自定义逻辑
     * 这里可以实现更复杂的条件组合逻辑
     */
    private boolean evaluateCustomLogic(List<StopLossCondition> conditions, MonitoringContext context) {
        // 示例：实现一个简单的自定义逻辑
        // 可以根据具体需求实现更复杂的逻辑表达式解析
        
        // 先评估所有条件
        Map<String, Boolean> results = new HashMap<>();
        for (StopLossCondition condition : conditions) {
            boolean result = evaluateCondition(condition, context);
            results.put(condition.getConditionId(), result);
        }
        
        // 这里可以实现自定义的逻辑表达式
        // 例如：(condition1 AND condition2) OR (condition3 AND condition4)
        // 目前简化为AND逻辑
        return results.values().stream().allMatch(Boolean::booleanValue);
    }
    
    /**
     * 获取条件评估结果摘要
     */
    public Map<String, Boolean> getEvaluationSummary(List<StopLossCondition> conditions, 
                                                   MonitoringContext context) {
        Map<String, Boolean> summary = new HashMap<>();
        
        for (StopLossCondition condition : conditions) {
            if (condition.isEnabled()) {
                Boolean cachedResult = context.getConditionResult(condition.getConditionId());
                if (cachedResult != null) {
                    summary.put(condition.getConditionId(), cachedResult);
                } else {
                    boolean result = evaluateCondition(condition, context);
                    summary.put(condition.getConditionId(), result);
                }
            }
        }
        
        return summary;
    }
    
    /**
     * 清除条件评估缓存
     */
    public void clearEvaluationCache(MonitoringContext context) {
        context.clearConditionResults();
    }
    
    /**
     * 获取已注册的条件处理器信息
     */
    public Map<StopLossCondition.ConditionType, String> getRegisteredHandlers() {
        Map<StopLossCondition.ConditionType, String> handlerInfo = new HashMap<>();
        
        for (Map.Entry<StopLossCondition.ConditionType, ConditionHandler> entry : conditionHandlers.entrySet()) {
            handlerInfo.put(entry.getKey(), entry.getValue().getHandlerDescription());
        }
        
        return handlerInfo;
    }
}
