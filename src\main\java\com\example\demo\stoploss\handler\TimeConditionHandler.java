package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.StopLossCondition;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间相关条件处理器
 * 处理时间窗口、交易时间等时间相关条件
 */
@Component
public class TimeConditionHandler implements ConditionHandler {
    
    @Override
    public boolean evaluate(StopLossCondition condition, MonitoringContext context) {
        if (!validateCondition(condition)) {
            return false;
        }
        
        try {
            switch (condition.getConditionType()) {
                case TIME_WINDOW:
                    return evaluateTimeWindow(condition, context);
                case TRADING_HOURS:
                    return evaluateTradingHours(condition, context);
                default:
                    return false;
            }
        } catch (Exception e) {
            context.setError("时间条件评估失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public StopLossCondition.ConditionType getSupportedConditionType() {
        return StopLossCondition.ConditionType.TIME_WINDOW;
    }
    
    @Override
    public boolean validateCondition(StopLossCondition condition) {
        if (condition == null || !condition.isEnabled()) {
            return false;
        }
        
        StopLossCondition.ConditionType type = condition.getConditionType();
        return type == StopLossCondition.ConditionType.TIME_WINDOW ||
               type == StopLossCondition.ConditionType.TRADING_HOURS;
    }
    
    /**
     * 评估时间窗口条件
     */
    private boolean evaluateTimeWindow(StopLossCondition condition, MonitoringContext context) {
        String startTimeStr = condition.getStringParameter("startTime");
        String endTimeStr = condition.getStringParameter("endTime");
        
        if (startTimeStr == null || endTimeStr == null) {
            return false;
        }
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            // 检查当前时间是否在指定的时间窗口内
            return !now.isBefore(startTime) && !now.isAfter(endTime);
        } catch (Exception e) {
            // 如果解析失败，尝试只解析时间部分
            return evaluateTimeOfDay(startTimeStr, endTimeStr);
        }
    }
    
    /**
     * 评估交易时间条件
     */
    private boolean evaluateTradingHours(StopLossCondition condition, MonitoringContext context) {
        String tradingStartStr = condition.getStringParameter("tradingStart");
        String tradingEndStr = condition.getStringParameter("tradingEnd");
        
        // 默认交易时间：9:30-15:00
        if (tradingStartStr == null) {
            tradingStartStr = "09:30:00";
        }
        if (tradingEndStr == null) {
            tradingEndStr = "15:00:00";
        }
        
        return evaluateTimeOfDay(tradingStartStr, tradingEndStr);
    }
    
    /**
     * 评估一天中的时间范围
     */
    private boolean evaluateTimeOfDay(String startTimeStr, String endTimeStr) {
        try {
            LocalTime now = LocalTime.now();
            LocalTime startTime = LocalTime.parse(startTimeStr);
            LocalTime endTime = LocalTime.parse(endTimeStr);
            
            // 处理跨天的情况
            if (startTime.isAfter(endTime)) {
                // 跨天情况：例如 22:00 到 06:00
                return !now.isBefore(startTime) || !now.isAfter(endTime);
            } else {
                // 同一天情况：例如 09:30 到 15:00
                return !now.isBefore(startTime) && !now.isAfter(endTime);
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String getHandlerDescription() {
        return "时间条件处理器：处理时间窗口、交易时间等时间相关条件";
    }
}
