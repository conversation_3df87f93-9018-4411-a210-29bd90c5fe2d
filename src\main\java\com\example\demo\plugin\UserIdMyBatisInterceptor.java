package com.example.demo.plugin;

import com.example.demo.model.RequestWithUser;
import com.example.demo.service.CurrentUserService;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.springframework.aop.framework.ProxyFactory;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.util.List;
import java.util.Properties;

/**
 * MyBatis 插件：在执行 SQL 设置参数前，自动注入 RequestWithUser 参数的 userId
 */
@Intercepts({
    @Signature(
        type = ParameterHandler.class,
        method = "setParameters",
        args = {PreparedStatement.class}
    )
})
@Component
public class UserIdMyBatisInterceptor implements Interceptor {

    @Autowired
    private CurrentUserService currentUserService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        ParameterHandler handler = (ParameterHandler) invocation.getTarget();
        // 仅在 SQL 绑定参数包含 userId 时才代理对象
        BoundSql boundSql = handler.getBoundSql();
        boolean hasUserIdParam = boundSql.getParameterMappings()
            .stream().anyMatch(m -> "userId".equals(m.getProperty()));
        Object param = handler.getParameterObject();
        if (hasUserIdParam && param instanceof RequestWithUser) {
            RequestWithUser original = (RequestWithUser) param;
            // 为原始对象创建代理，拦截 getUserId 方法
            ProxyFactory factory = new ProxyFactory(original);
            factory.setProxyTargetClass(true);
            factory.addAdvice((MethodInterceptor) invocation2 -> {
                if ("getUserId".equals(invocation2.getMethod().getName())) {
                    Long id = original.getUserId();
                    if (id == null) {
                        id = currentUserService.getCurrentUserId();
                        original.setUserId(id);
                    }
                    return id;
                }
                return invocation2.proceed();
            });
            RequestWithUser proxy = (RequestWithUser) factory.getProxy();
            // 用代理替换 handler 的参数对象
            try {
                Field f = handler.getClass().getDeclaredField("parameterObject");
                f.setAccessible(true);
                f.set(handler, proxy);
            } catch (NoSuchFieldException ignored) {}
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // no properties
    }
}
