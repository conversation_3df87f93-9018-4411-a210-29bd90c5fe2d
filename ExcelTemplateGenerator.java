import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ExcelTemplateGenerator {
    
    public static void main(String[] args) {
        // 1. Prepare data for hidden sheet
        List<CityMapping> cityMappings = new ArrayList<>();
        cityMappings.add(new CityMapping(101, "北京"));
        cityMappings.add(new CityMapping(102, "上海"));
        cityMappings.add(new CityMapping(103, "广州"));

        // 2. Create Excel file
        String fileName = "CityDropdownTemplate.xlsx";
        File file = new File(fileName);
        
        // 3. Write hidden sheet with mappings
        WriteSheet mappingSheet = EasyExcel.writerSheet("Sheet2").build();
        EasyExcel.write(file).head(CityMapping.class).sheet().doWrite(cityMappings);
        
        // 4. Reopen workbook to add data validation and formulas
        Workbook workbook = WorkbookFactory.create(file);
        Sheet mainSheet = workbook.createSheet("Sheet1");
        
        // 5. Create data validation (dropdown)
        DataValidationHelper dvHelper = mainSheet.getDataValidationHelper();
        DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("Sheet2!$B$2:$B$4");
        CellRangeAddressList addressList = new CellRangeAddressList(1, 100, 2, 2); // C2:C100
        DataValidation validation = dvHelper.createValidation(dvConstraint, addressList);
        mainSheet.addValidationData(validation);
        
        // 6. Add formula to lookup ID
        Row headerRow = mainSheet.createRow(0);
        headerRow.createCell(2).setCellValue("城市名称");
        headerRow.createCell(3).setCellValue("城市ID");
        
        // 7. Hide the ID column
        mainSheet.setColumnHidden(3, true);
        
        // 8. Save changes
        try (FileOutputStream fos = new FileOutputStream(file)) {
            workbook.write(fos);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        System.out.println("Template created: " + fileName);
    }
    
    public static class CityMapping {
        @ExcelProperty("ID")
        private int id;
        @ExcelProperty("名称")
        private String name;
        
        public CityMapping(int id, String name) {
            this.id = id;
            this.name = name;
        }
        
        // Getters and setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
}
