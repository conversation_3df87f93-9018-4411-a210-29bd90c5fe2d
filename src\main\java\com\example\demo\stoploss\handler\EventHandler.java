package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.StopLossEvent;

/**
 * 事件处理器接口
 * 定义事件执行的标准接口
 */
public interface EventHandler {
    
    /**
     * 执行事件
     * 
     * @param event 要执行的事件
     * @param context 监控上下文
     * @return 执行结果，true表示成功
     */
    boolean execute(StopLossEvent event, MonitoringContext context);
    
    /**
     * 获取处理器支持的事件类型
     * 
     * @return 支持的事件类型
     */
    StopLossEvent.EventType getSupportedEventType();
    
    /**
     * 验证事件配置是否有效
     * 
     * @param event 要验证的事件
     * @return 验证结果，true表示有效
     */
    default boolean validateEvent(StopLossEvent event) {
        return event != null && 
               event.getEventType() == getSupportedEventType() &&
               event.isEnabled();
    }
    
    /**
     * 检查是否支持异步执行
     * 
     * @return true表示支持异步执行
     */
    default boolean supportsAsync() {
        return true;
    }
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取处理器描述
     * 
     * @return 处理器描述
     */
    default String getHandlerDescription() {
        return "事件处理器：" + getSupportedEventType().getDescription();
    }
    
    /**
     * 事件执行前的预处理
     * 
     * @param event 要执行的事件
     * @param context 监控上下文
     * @return 是否继续执行，false表示跳过执行
     */
    default boolean preExecute(StopLossEvent event, MonitoringContext context) {
        return true;
    }
    
    /**
     * 事件执行后的后处理
     * 
     * @param event 已执行的事件
     * @param context 监控上下文
     * @param success 执行是否成功
     */
    default void postExecute(StopLossEvent event, MonitoringContext context, boolean success) {
        // 记录事件执行时间
        if (success) {
            context.recordEventExecution(event.getEventId());
        }
    }
}
