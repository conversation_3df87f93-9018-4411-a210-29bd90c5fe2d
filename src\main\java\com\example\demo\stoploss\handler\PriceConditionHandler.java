package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.PensionPortfolio;
import com.example.demo.stoploss.model.StopLossCondition;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 价格相关条件处理器
 * 处理净值下跌、收益率亏损、最大回撤等价格相关条件
 */
@Component
public class PriceConditionHandler implements ConditionHandler {
    
    @Override
    public boolean evaluate(StopLossCondition condition, MonitoringContext context) {
        if (!validateCondition(condition)) {
            return false;
        }
        
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio == null) {
            return false;
        }
        
        try {
            switch (condition.getConditionType()) {
                case NET_VALUE_DROP:
                    return evaluateNetValueDrop(condition, portfolio);
                case DAILY_RETURN_LOSS:
                    return evaluateDailyReturnLoss(condition, portfolio);
                case CUMULATIVE_RETURN_LOSS:
                    return evaluateCumulativeReturnLoss(condition, portfolio);
                case MAX_DRAWDOWN:
                    return evaluateMaxDrawdown(condition, portfolio);
                default:
                    return false;
            }
        } catch (Exception e) {
            // 记录错误日志
            context.setError("价格条件评估失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public StopLossCondition.ConditionType getSupportedConditionType() {
        // 这个处理器支持多种价格相关条件，这里返回主要类型
        return StopLossCondition.ConditionType.NET_VALUE_DROP;
    }
    
    @Override
    public boolean validateCondition(StopLossCondition condition) {
        if (condition == null || !condition.isEnabled()) {
            return false;
        }
        
        // 检查是否为支持的价格相关条件类型
        StopLossCondition.ConditionType type = condition.getConditionType();
        return type == StopLossCondition.ConditionType.NET_VALUE_DROP ||
               type == StopLossCondition.ConditionType.DAILY_RETURN_LOSS ||
               type == StopLossCondition.ConditionType.CUMULATIVE_RETURN_LOSS ||
               type == StopLossCondition.ConditionType.MAX_DRAWDOWN;
    }
    
    /**
     * 评估净值下跌条件
     */
    private boolean evaluateNetValueDrop(StopLossCondition condition, PensionPortfolio portfolio) {
        BigDecimal threshold = condition.getDecimalParameter("threshold");
        if (threshold == null) {
            return false;
        }
        
        BigDecimal currentNetValue = portfolio.getCurrentNetValue();
        BigDecimal previousNetValue = portfolio.getPreviousNetValue();
        
        if (currentNetValue == null || previousNetValue == null || 
            previousNetValue.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        
        // 计算净值下跌百分比
        BigDecimal dropPercentage = previousNetValue.subtract(currentNetValue)
                .divide(previousNetValue, 6, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        
        return dropPercentage.compareTo(threshold) >= 0;
    }
    
    /**
     * 评估日收益率亏损条件
     */
    private boolean evaluateDailyReturnLoss(StopLossCondition condition, PensionPortfolio portfolio) {
        BigDecimal threshold = condition.getDecimalParameter("threshold");
        if (threshold == null) {
            return false;
        }
        
        BigDecimal dailyReturn = portfolio.getDailyReturn();
        if (dailyReturn == null) {
            // 如果没有直接的日收益率，尝试计算
            dailyReturn = portfolio.calculateDailyReturn();
        }
        
        if (dailyReturn == null) {
            return false;
        }
        
        // 将日收益率转换为百分比
        BigDecimal dailyReturnPercentage = dailyReturn.multiply(BigDecimal.valueOf(100));
        
        // 检查是否低于阈值（负值表示亏损）
        return dailyReturnPercentage.compareTo(threshold) <= 0;
    }
    
    /**
     * 评估累计收益率亏损条件
     */
    private boolean evaluateCumulativeReturnLoss(StopLossCondition condition, PensionPortfolio portfolio) {
        BigDecimal threshold = condition.getDecimalParameter("threshold");
        if (threshold == null) {
            return false;
        }
        
        BigDecimal cumulativeReturn = portfolio.getCumulativeReturn();
        if (cumulativeReturn == null) {
            return false;
        }
        
        // 将累计收益率转换为百分比
        BigDecimal cumulativeReturnPercentage = cumulativeReturn.multiply(BigDecimal.valueOf(100));
        
        // 检查是否低于阈值
        return cumulativeReturnPercentage.compareTo(threshold) <= 0;
    }
    
    /**
     * 评估最大回撤条件
     */
    private boolean evaluateMaxDrawdown(StopLossCondition condition, PensionPortfolio portfolio) {
        BigDecimal threshold = condition.getDecimalParameter("threshold");
        if (threshold == null) {
            return false;
        }
        
        BigDecimal maxDrawdown = portfolio.getMaxDrawdown();
        if (maxDrawdown == null) {
            return false;
        }
        
        // 将最大回撤转换为百分比（通常已经是百分比形式）
        BigDecimal maxDrawdownPercentage = maxDrawdown.multiply(BigDecimal.valueOf(100));
        
        // 检查是否超过阈值
        return maxDrawdownPercentage.compareTo(threshold) >= 0;
    }
    
    @Override
    public String getHandlerDescription() {
        return "价格条件处理器：处理净值下跌、收益率亏损、最大回撤等价格相关条件";
    }
}
