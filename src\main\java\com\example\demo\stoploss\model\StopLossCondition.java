package com.example.demo.stoploss.model;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 止损条件定义
 * 支持多种条件类型和参数配置
 */
@Data
public class StopLossCondition {
    
    /**
     * 条件ID
     */
    private String conditionId;
    
    /**
     * 条件名称
     */
    private String conditionName;
    
    /**
     * 条件类型
     */
    private ConditionType conditionType;
    
    /**
     * 条件参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 是否启用
     */
    private boolean enabled;
    
    /**
     * 条件描述
     */
    private String description;
    
    /**
     * 条件类型枚举
     */
    public enum ConditionType {
        // 价格相关条件
        NET_VALUE_DROP("净值下跌", "net_value_drop"),
        DAILY_RETURN_LOSS("日收益率亏损", "daily_return_loss"),
        CUMULATIVE_RETURN_LOSS("累计收益率亏损", "cumulative_return_loss"),
        MAX_DRAWDOWN("最大回撤", "max_drawdown"),
        
        // 成交量相关条件
        VOLUME_SPIKE("成交量异常", "volume_spike"),
        VOLUME_DROP("成交量萎缩", "volume_drop"),
        
        // 时间相关条件
        TIME_WINDOW("时间窗口", "time_window"),
        TRADING_HOURS("交易时间", "trading_hours"),
        
        // 波动率相关条件
        VOLATILITY_HIGH("高波动率", "volatility_high"),
        VOLATILITY_LOW("低波动率", "volatility_low"),
        
        // 复合条件
        AND_CONDITION("AND条件", "and_condition"),
        OR_CONDITION("OR条件", "or_condition"),
        
        // 自定义条件
        CUSTOM("自定义条件", "custom");
        
        private final String description;
        private final String code;
        
        ConditionType(String description, String code) {
            this.description = description;
            this.code = code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getCode() {
            return code;
        }
    }
    
    /**
     * 获取参数值
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }
    
    /**
     * 获取参数值（BigDecimal类型）
     */
    public BigDecimal getDecimalParameter(String key) {
        Object value = getParameter(key);
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 获取参数值（Integer类型）
     */
    public Integer getIntegerParameter(String key) {
        Object value = getParameter(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.valueOf((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 获取参数值（String类型）
     */
    public String getStringParameter(String key) {
        Object value = getParameter(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 设置参数值
     */
    public void setParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new java.util.HashMap<>();
        }
        parameters.put(key, value);
    }
    
    /**
     * 创建净值下跌条件
     */
    public static StopLossCondition createNetValueDropCondition(String conditionId, BigDecimal threshold) {
        StopLossCondition condition = new StopLossCondition();
        condition.setConditionId(conditionId);
        condition.setConditionName("净值下跌条件");
        condition.setConditionType(ConditionType.NET_VALUE_DROP);
        condition.setParameter("threshold", threshold);
        condition.setEnabled(true);
        condition.setDescription("当净值下跌超过" + threshold + "%时触发");
        return condition;
    }
    
    /**
     * 创建日收益率亏损条件
     */
    public static StopLossCondition createDailyReturnLossCondition(String conditionId, BigDecimal threshold) {
        StopLossCondition condition = new StopLossCondition();
        condition.setConditionId(conditionId);
        condition.setConditionName("日收益率亏损条件");
        condition.setConditionType(ConditionType.DAILY_RETURN_LOSS);
        condition.setParameter("threshold", threshold);
        condition.setEnabled(true);
        condition.setDescription("当日收益率低于" + threshold + "%时触发");
        return condition;
    }
    
    /**
     * 创建最大回撤条件
     */
    public static StopLossCondition createMaxDrawdownCondition(String conditionId, BigDecimal threshold) {
        StopLossCondition condition = new StopLossCondition();
        condition.setConditionId(conditionId);
        condition.setConditionName("最大回撤条件");
        condition.setConditionType(ConditionType.MAX_DRAWDOWN);
        condition.setParameter("threshold", threshold);
        condition.setEnabled(true);
        condition.setDescription("当最大回撤超过" + threshold + "%时触发");
        return condition;
    }
}
