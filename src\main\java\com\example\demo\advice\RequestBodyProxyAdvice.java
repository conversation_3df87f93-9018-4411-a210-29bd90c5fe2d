package com.example.demo.advice;

import com.example.demo.model.RequestWithUser;
import com.example.demo.service.CurrentUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;
import org.springframework.aop.framework.ProxyFactory;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

@ControllerAdvice
public class RequestBodyProxyAdvice extends RequestBodyAdviceAdapter {

    @Autowired
    private CurrentUserService currentUserService;

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
                            Class<? extends HttpMessageConverter<?>> converterType) {
        // 仅拦截实现 RequestWithUser 接口的入参
        return RequestWithUser.class.isAssignableFrom(methodParameter.getParameterType());
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage,
                                MethodParameter parameter, Type targetType,
                                Class<? extends HttpMessageConverter<?>> converterType) {
        if (body instanceof RequestWithUser) {
            RequestWithUser target = (RequestWithUser) body;
            ProxyFactory factory = new ProxyFactory(target);
            factory.setProxyTargetClass(true);
            factory.addAdvice((MethodInterceptor) invocation -> {
                String methodName = invocation.getMethod().getName();
                if ("getUserId".equals(methodName)) {
                    Long id = target.getUserId();
                    if (id == null) {
                        id = currentUserService.getCurrentUserId();
                        target.setUserId(id);
                    }
                    return id;
                }
                return invocation.proceed();
            });
            return factory.getProxy();
        }
        return body;
    }
}
