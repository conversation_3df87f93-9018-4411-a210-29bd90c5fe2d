package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.PensionPortfolio;
import com.example.demo.stoploss.model.StopLossEvent;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 交易事件处理器
 * 处理停止交易、减仓、平仓、对冲等交易类事件
 */
@Component
public class TradingEventHandler implements EventHandler {
    
    @Override
    public boolean execute(StopLossEvent event, MonitoringContext context) {
        if (!validateEvent(event)) {
            return false;
        }
        
        try {
            switch (event.getEventType()) {
                case STOP_TRADING:
                    return stopTrading(event, context);
                case REDUCE_POSITION:
                    return reducePosition(event, context);
                case CLOSE_POSITION:
                    return closePosition(event, context);
                case HEDGE_POSITION:
                    return hedgePosition(event, context);
                case RESUME_TRADING:
                    return resumeTrading(event, context);
                default:
                    return false;
            }
        } catch (Exception e) {
            System.err.println("交易事件执行失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public StopLossEvent.EventType getSupportedEventType() {
        return StopLossEvent.EventType.STOP_TRADING;
    }
    
    @Override
    public boolean validateEvent(StopLossEvent event) {
        if (event == null || !event.isEnabled()) {
            return false;
        }
        
        StopLossEvent.EventType type = event.getEventType();
        return type == StopLossEvent.EventType.STOP_TRADING ||
               type == StopLossEvent.EventType.REDUCE_POSITION ||
               type == StopLossEvent.EventType.CLOSE_POSITION ||
               type == StopLossEvent.EventType.HEDGE_POSITION ||
               type == StopLossEvent.EventType.RESUME_TRADING;
    }
    
    @Override
    public boolean supportsAsync() {
        // 交易操作通常需要同步执行以确保及时性
        return false;
    }
    
    /**
     * 停止交易
     */
    private boolean stopTrading(StopLossEvent event, MonitoringContext context) {
        String portfolioId = event.getStringParameter("portfolioId");
        if (portfolioId == null) {
            portfolioId = context.getPortfolio().getPortfolioId();
        }
        
        // 模拟停止交易操作
        System.out.println("=== 停止交易 ===");
        System.out.println("组合ID: " + portfolioId);
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("操作: 停止所有交易活动");
        
        // 更新组合状态
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio != null) {
            portfolio.setStopLossStatus(PensionPortfolio.StopLossStatus.STOP_LOSS_TRIGGERED);
            portfolio.setExtendedProperty("tradingStatus", "STOPPED");
            portfolio.setExtendedProperty("stopTradingTime", LocalDateTime.now());
        }
        
        // 在实际实现中，这里应该调用交易系统API
        // tradingService.stopTrading(portfolioId);
        
        System.out.println("交易已停止");
        System.out.println("================");
        
        return true;
    }
    
    /**
     * 减仓
     */
    private boolean reducePosition(StopLossEvent event, MonitoringContext context) {
        String portfolioId = event.getStringParameter("portfolioId");
        String reductionRatioStr = event.getStringParameter("reductionRatio");
        
        if (portfolioId == null) {
            portfolioId = context.getPortfolio().getPortfolioId();
        }
        
        BigDecimal reductionRatio = BigDecimal.valueOf(0.5); // 默认减仓50%
        if (reductionRatioStr != null) {
            try {
                reductionRatio = new BigDecimal(reductionRatioStr);
            } catch (NumberFormatException e) {
                System.err.println("减仓比例格式错误，使用默认值50%");
            }
        }
        
        // 模拟减仓操作
        System.out.println("=== 减仓操作 ===");
        System.out.println("组合ID: " + portfolioId);
        System.out.println("减仓比例: " + reductionRatio.multiply(BigDecimal.valueOf(100)) + "%");
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 在实际实现中，这里应该调用交易系统API
        // tradingService.reducePosition(portfolioId, reductionRatio);
        
        System.out.println("减仓操作已执行");
        System.out.println("================");
        
        return true;
    }
    
    /**
     * 平仓
     */
    private boolean closePosition(StopLossEvent event, MonitoringContext context) {
        String portfolioId = event.getStringParameter("portfolioId");
        if (portfolioId == null) {
            portfolioId = context.getPortfolio().getPortfolioId();
        }
        
        // 模拟平仓操作
        System.out.println("=== 平仓操作 ===");
        System.out.println("组合ID: " + portfolioId);
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("操作: 清空所有持仓");
        
        // 在实际实现中，这里应该调用交易系统API
        // tradingService.closeAllPositions(portfolioId);
        
        System.out.println("平仓操作已执行");
        System.out.println("================");
        
        return true;
    }
    
    /**
     * 对冲
     */
    private boolean hedgePosition(StopLossEvent event, MonitoringContext context) {
        String portfolioId = event.getStringParameter("portfolioId");
        String hedgeStrategy = event.getStringParameter("hedgeStrategy");
        
        if (portfolioId == null) {
            portfolioId = context.getPortfolio().getPortfolioId();
        }
        if (hedgeStrategy == null) {
            hedgeStrategy = "DEFAULT";
        }
        
        // 模拟对冲操作
        System.out.println("=== 对冲操作 ===");
        System.out.println("组合ID: " + portfolioId);
        System.out.println("对冲策略: " + hedgeStrategy);
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 在实际实现中，这里应该调用交易系统API
        // tradingService.executeHedge(portfolioId, hedgeStrategy);
        
        System.out.println("对冲操作已执行");
        System.out.println("================");
        
        return true;
    }
    
    /**
     * 恢复交易
     */
    private boolean resumeTrading(StopLossEvent event, MonitoringContext context) {
        String portfolioId = event.getStringParameter("portfolioId");
        if (portfolioId == null) {
            portfolioId = context.getPortfolio().getPortfolioId();
        }
        
        // 模拟恢复交易操作
        System.out.println("=== 恢复交易 ===");
        System.out.println("组合ID: " + portfolioId);
        System.out.println("执行时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("操作: 恢复交易活动");
        
        // 更新组合状态
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio != null) {
            portfolio.setStopLossStatus(PensionPortfolio.StopLossStatus.RECOVERED);
            portfolio.setExtendedProperty("tradingStatus", "ACTIVE");
            portfolio.setExtendedProperty("resumeTradingTime", LocalDateTime.now());
        }
        
        // 在实际实现中，这里应该调用交易系统API
        // tradingService.resumeTrading(portfolioId);
        
        System.out.println("交易已恢复");
        System.out.println("================");
        
        return true;
    }
    
    @Override
    public String getHandlerDescription() {
        return "交易事件处理器：处理停止交易、减仓、平仓、对冲、恢复交易等交易类事件";
    }
}
