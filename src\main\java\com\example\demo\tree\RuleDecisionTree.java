package com.example.demo.tree;

import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import lombok.Getter;

import java.util.List;

/**
 * 规则决策树
 */
public class RuleDecisionTree {
    // 根节点
    private final RuleDecisionTreeNode root;
    // 属性顺序
    private final List<String> attributeOrder;
    /**
     * -- GETTER --
     *  获取规则数量
     *
     * @return 规则数量
     */
    // 规则数量
    @Getter
    private final int ruleCount;

    // 构造函数
    public RuleDecisionTree(List<AreaSplitStaticRuleDTO> rules, List<String> attributeOrder) {
        this.attributeOrder = attributeOrder;
        // 传递第一个属性名称
        this.root = new RuleDecisionTreeNode(attributeOrder.get(0));
        this.ruleCount = rules.size();

        // 构建决策树 - 保持原始规则顺序
        for (int i = 0; i < ruleCount; i++) {
            root.addRule(rules.get(i), attributeOrder, 0);
        }
    }

    /**
     * 查找匹配规则
     * @param dto 输入参数
     * @return 匹配的规则列表
     */
    public List<AreaSplitStaticRuleDTO> findMatchingRules(ShrAreaSplitDTO dto) {
        return root.findMatchingRules(dto, attributeOrder, 0);
    }

}
