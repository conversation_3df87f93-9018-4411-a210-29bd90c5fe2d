package com.example.demo.util;

import java.util.ArrayList;
import java.util.Map;

/**
 * 层级关系匹配工具类
 * 用于检查规则和数据之间的层级关系匹配
 */
public class HierarchyMatcher {

    /**
     * 检查规则机构是否匹配数据机构
     * 规则适用于上层，数据来自下层
     * 如果规则机构在数据机构的层级列表中，则匹配成功
     * 
     * @param ruleAgency 规则机构
     * @param dtoAgency 数据机构
     * @param hierarchy 机构层级关系
     * @return 是否匹配
     */
    public static boolean isAgencyMatch(String ruleAgency, String dtoAgency, Map<String, ArrayList<String>> hierarchy) {
        // 如果规则机构是默认值，则匹配所有机构
        if ("-1".equals(ruleAgency)) {
            return true;
        }
        
        // 获取数据机构的层级列表
        ArrayList<String> agencyHierarchy = hierarchy.get(dtoAgency);
        if (agencyHierarchy == null) {
            return ruleAgency.equals(dtoAgency);
        }
        
        // 如果规则机构在数据机构的层级列表中，则匹配成功
        return agencyHierarchy.contains(ruleAgency);
    }
    
    /**
     * 检查规则区域是否匹配数据区域
     * 规则适用于上层，数据来自下层
     * 如果规则区域在数据区域的层级列表中，则匹配成功
     * 
     * @param ruleRegion 规则区域
     * @param dtoRegion 数据区域
     * @param hierarchy 区域层级关系
     * @return 是否匹配
     */
    public static boolean isRegionMatch(String ruleRegion, String dtoRegion, Map<String, ArrayList<String>> hierarchy) {
        // 如果规则区域是默认值，则匹配所有区域
        if ("-1".equals(ruleRegion)) {
            return true;
        }
        
        // 获取数据区域的层级列表
        ArrayList<String> regionHierarchy = hierarchy.get(dtoRegion);
        if (regionHierarchy == null) {
            return ruleRegion.equals(dtoRegion);
        }
        
        // 如果规则区域在数据区域的层级列表中，则匹配成功
        return regionHierarchy.contains(ruleRegion);
    }
}
