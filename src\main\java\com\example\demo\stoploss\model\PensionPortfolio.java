package com.example.demo.stoploss.model;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 养老金组合模型
 * 包含组合的基本信息和实时数据
 */
@Data
public class PensionPortfolio {
    
    /**
     * 组合ID
     */
    private String portfolioId;
    
    /**
     * 组合名称
     */
    private String portfolioName;
    
    /**
     * 组合类型
     */
    private String portfolioType;
    
    /**
     * 当前净值
     */
    private BigDecimal currentNetValue;
    
    /**
     * 前一日净值
     */
    private BigDecimal previousNetValue;
    
    /**
     * 当前总资产
     */
    private BigDecimal totalAssets;
    
    /**
     * 当日收益率
     */
    private BigDecimal dailyReturn;
    
    /**
     * 累计收益率
     */
    private BigDecimal cumulativeReturn;
    
    /**
     * 最大回撤
     */
    private BigDecimal maxDrawdown;
    
    /**
     * 当前成交量
     */
    private Long currentVolume;
    
    /**
     * 平均成交量（过去N天）
     */
    private Long averageVolume;
    
    /**
     * 波动率
     */
    private BigDecimal volatility;
    
    /**
     * 止损状态
     */
    private StopLossStatus stopLossStatus;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 扩展属性，用于存储其他监控指标
     */
    private Map<String, Object> extendedProperties;
    
    /**
     * 止损状态枚举
     */
    public enum StopLossStatus {
        NORMAL("正常"),
        STOP_LOSS_TRIGGERED("止损触发"),
        RECOVERING("恢复中"),
        RECOVERED("已恢复");
        
        private final String description;
        
        StopLossStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取扩展属性值
     */
    public Object getExtendedProperty(String key) {
        return extendedProperties != null ? extendedProperties.get(key) : null;
    }
    
    /**
     * 设置扩展属性值
     */
    public void setExtendedProperty(String key, Object value) {
        if (extendedProperties == null) {
            extendedProperties = new java.util.HashMap<>();
        }
        extendedProperties.put(key, value);
    }
    
    /**
     * 计算当日收益率
     */
    public BigDecimal calculateDailyReturn() {
        if (previousNetValue == null || previousNetValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return currentNetValue.subtract(previousNetValue)
                .divide(previousNetValue, 6, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 判断是否处于止损状态
     */
    public boolean isInStopLoss() {
        return stopLossStatus == StopLossStatus.STOP_LOSS_TRIGGERED;
    }
    
    /**
     * 判断是否可以恢复
     */
    public boolean canRecover() {
        return stopLossStatus == StopLossStatus.STOP_LOSS_TRIGGERED || 
               stopLossStatus == StopLossStatus.RECOVERING;
    }
}
