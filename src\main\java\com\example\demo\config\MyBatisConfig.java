package com.example.demo.config;

import com.example.demo.plugin.UserIdMyBatisInterceptor;
import com.example.demo.plugin.MybatisResultProxyInterceptor;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisConfig {
    @Bean
    public ConfigurationCustomizer mybatisInterceptorCustomizer(
            UserIdMyBatisInterceptor userIdMyBatisInterceptor,
            MybatisResultProxyInterceptor mybatisResultProxyInterceptor) {
        return configuration -> {
            configuration.addInterceptor(userIdMyBatisInterceptor);
            configuration.addInterceptor(mybatisResultProxyInterceptor);
        };
    }
}
