package com.example.demo.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.example.demo.annotation.ExcelSelected;

public class CityMapping {
    @ExcelProperty("ID")
    private String id;
    
    @ExcelProperty("名称")
    @ExcelSelected("city")
    private String name;
    
    @ExcelProperty("城市ID")
    private String cityId; // This will be auto-populated by formula
    
    public CityMapping() {}
    
    public CityMapping(String id, String name) {
        this.id = id;
        this.name = name;
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getCityId() { return cityId; }
    public void setCityId(String cityId) { this.cityId = cityId; }
}
