package com.example.demo.stoploss;

import com.example.demo.stoploss.model.*;
import com.example.demo.stoploss.service.RuleConfigurationService;
import com.example.demo.stoploss.service.StopLossMonitoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 止损监控引擎演示
 * 展示如何配置和使用止损监控系统
 */
@Component
public class StopLossMonitoringDemo implements CommandLineRunner {
    
    @Autowired
    private StopLossMonitoringService monitoringService;
    
    @Autowired
    private RuleConfigurationService ruleConfigurationService;
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== 养老金组合止损监控引擎演示 ===");
        
        // 创建示例组合
        PensionPortfolio portfolio = createSamplePortfolio();
        
        // 配置止损规则
        configureStopLossRules();
        
        // 启动监控
        monitoringService.startMonitoring(portfolio);
        
        // 模拟数据变化和监控过程
        simulateMonitoringProcess(portfolio);
        
        System.out.println("=== 演示完成 ===");
    }
    
    /**
     * 创建示例组合
     */
    private PensionPortfolio createSamplePortfolio() {
        PensionPortfolio portfolio = new PensionPortfolio();
        portfolio.setPortfolioId("PENSION_001");
        portfolio.setPortfolioName("稳健型养老金组合");
        portfolio.setPortfolioType("CONSERVATIVE");
        portfolio.setCurrentNetValue(BigDecimal.valueOf(1.0500));
        portfolio.setPreviousNetValue(BigDecimal.valueOf(1.0600));
        portfolio.setTotalAssets(BigDecimal.valueOf(100000000)); // 1亿
        portfolio.setDailyReturn(BigDecimal.valueOf(-0.0094)); // -0.94%
        portfolio.setCumulativeReturn(BigDecimal.valueOf(0.05)); // 5%
        portfolio.setMaxDrawdown(BigDecimal.valueOf(0.02)); // 2%
        portfolio.setCurrentVolume(50000L);
        portfolio.setAverageVolume(30000L);
        portfolio.setVolatility(BigDecimal.valueOf(0.15)); // 15%
        portfolio.setStopLossStatus(PensionPortfolio.StopLossStatus.NORMAL);
        portfolio.setLastUpdateTime(LocalDateTime.now());
        
        System.out.println("创建示例组合: " + portfolio.getPortfolioId());
        return portfolio;
    }
    
    /**
     * 配置止损规则
     */
    private void configureStopLossRules() {
        // 规则1：日收益率止损规则
        StopLossRule dailyReturnRule = createDailyReturnStopLossRule();
        ruleConfigurationService.addRule(dailyReturnRule);
        
        // 规则2：最大回撤止损规则
        StopLossRule maxDrawdownRule = createMaxDrawdownStopLossRule();
        ruleConfigurationService.addRule(maxDrawdownRule);
        
        // 规则3：综合止损规则（带恢复功能）
        StopLossRule comprehensiveRule = createComprehensiveStopLossRule();
        ruleConfigurationService.addRule(comprehensiveRule);
        
        System.out.println("配置了 " + ruleConfigurationService.getAllRules().size() + " 个止损规则");
    }
    
    /**
     * 创建日收益率止损规则
     */
    private StopLossRule createDailyReturnStopLossRule() {
        // 条件：日收益率低于-1%
        StopLossCondition condition = StopLossCondition.createDailyReturnLossCondition(
                "DAILY_RETURN_CONDITION", BigDecimal.valueOf(-1.0));
        
        // 事件：发送邮件通知 + 风险预警
        StopLossEvent emailEvent = StopLossEvent.createEmailNotificationEvent(
                "EMAIL_ALERT", "<EMAIL>", 
                "日收益率止损预警 - {portfolioName}", 
                "组合{portfolioId}日收益率为{dailyReturn}%，已触发止损预警。");
        
        StopLossEvent riskEvent = StopLossEvent.createRiskAlertEvent(
                "RISK_ALERT", "PENSION_001", "HIGH");
        
        return StopLossRule.createSimpleStopLossRule(
                "DAILY_RETURN_RULE", "日收益率止损规则",
                Arrays.asList("PENSION_001"),
                Arrays.asList(condition),
                Arrays.asList(emailEvent, riskEvent));
    }
    
    /**
     * 创建最大回撤止损规则
     */
    private StopLossRule createMaxDrawdownStopLossRule() {
        // 条件：最大回撤超过5%
        StopLossCondition condition = StopLossCondition.createMaxDrawdownCondition(
                "MAX_DRAWDOWN_CONDITION", BigDecimal.valueOf(5.0));
        
        // 事件：停止交易 + 发送通知
        StopLossEvent stopTradingEvent = StopLossEvent.createStopTradingEvent(
                "STOP_TRADING", "PENSION_001");
        
        StopLossEvent notificationEvent = StopLossEvent.createEmailNotificationEvent(
                "MAX_DRAWDOWN_EMAIL", "<EMAIL>",
                "最大回撤止损触发 - {portfolioName}",
                "组合{portfolioId}最大回撤达到{maxDrawdown}%，已执行止损操作。");
        
        return StopLossRule.createSimpleStopLossRule(
                "MAX_DRAWDOWN_RULE", "最大回撤止损规则",
                Arrays.asList("PENSION_001"),
                Arrays.asList(condition),
                Arrays.asList(stopTradingEvent, notificationEvent));
    }
    
    /**
     * 创建综合止损规则（带恢复功能）
     */
    private StopLossRule createComprehensiveStopLossRule() {
        // 止损条件：日收益率低于-2% 且 成交量异常
        StopLossCondition returnCondition = StopLossCondition.createDailyReturnLossCondition(
                "SEVERE_RETURN_LOSS", BigDecimal.valueOf(-2.0));
        
        StopLossCondition volumeCondition = new StopLossCondition();
        volumeCondition.setConditionId("VOLUME_SPIKE");
        volumeCondition.setConditionName("成交量异常");
        volumeCondition.setConditionType(StopLossCondition.ConditionType.VOLUME_SPIKE);
        volumeCondition.setParameter("thresholdMultiplier", BigDecimal.valueOf(2.0));
        volumeCondition.setEnabled(true);
        
        // 止损事件：停止交易 + 减仓 + 通知
        StopLossEvent stopEvent = StopLossEvent.createStopTradingEvent("EMERGENCY_STOP", "PENSION_001");
        StopLossEvent reduceEvent = new StopLossEvent();
        reduceEvent.setEventId("REDUCE_POSITION");
        reduceEvent.setEventName("紧急减仓");
        reduceEvent.setEventType(StopLossEvent.EventType.REDUCE_POSITION);
        reduceEvent.setParameter("portfolioId", "PENSION_001");
        reduceEvent.setParameter("reductionRatio", "0.3");
        reduceEvent.setPriority(1);
        reduceEvent.setEnabled(true);
        reduceEvent.setAsync(false);
        
        StopLossEvent alertEvent = StopLossEvent.createEmailNotificationEvent(
                "EMERGENCY_ALERT", "<EMAIL>",
                "紧急止损触发 - {portfolioName}",
                "组合{portfolioId}触发紧急止损条件，已执行止损措施。");
        
        // 恢复条件：日收益率回升到-0.5%以上 且 交易时间内
        StopLossCondition recoveryReturnCondition = new StopLossCondition();
        recoveryReturnCondition.setConditionId("RECOVERY_RETURN");
        recoveryReturnCondition.setConditionName("收益率恢复");
        recoveryReturnCondition.setConditionType(StopLossCondition.ConditionType.DAILY_RETURN_LOSS);
        recoveryReturnCondition.setParameter("threshold", BigDecimal.valueOf(-0.5));
        recoveryReturnCondition.setEnabled(true);
        
        StopLossCondition tradingTimeCondition = new StopLossCondition();
        tradingTimeCondition.setConditionId("TRADING_TIME");
        tradingTimeCondition.setConditionName("交易时间");
        tradingTimeCondition.setConditionType(StopLossCondition.ConditionType.TRADING_HOURS);
        tradingTimeCondition.setEnabled(true);
        
        // 恢复事件：恢复交易 + 通知
        StopLossEvent resumeEvent = StopLossEvent.createResumeTradingEvent("RESUME_TRADING", "PENSION_001");
        StopLossEvent recoveryNotification = StopLossEvent.createEmailNotificationEvent(
                "RECOVERY_NOTIFICATION", "<EMAIL>",
                "止损恢复通知 - {portfolioName}",
                "组合{portfolioId}满足恢复条件，已恢复正常交易。");
        
        return StopLossRule.createRecoverableStopLossRule(
                "COMPREHENSIVE_RULE", "综合止损规则",
                Arrays.asList("PENSION_001"),
                Arrays.asList(returnCondition, volumeCondition),
                Arrays.asList(stopEvent, reduceEvent, alertEvent),
                Arrays.asList(recoveryReturnCondition, tradingTimeCondition),
                Arrays.asList(resumeEvent, recoveryNotification));
    }
    
    /**
     * 模拟监控过程
     */
    private void simulateMonitoringProcess(PensionPortfolio portfolio) throws InterruptedException {
        System.out.println("\n=== 开始模拟监控过程 ===");
        
        // 等待一段时间让监控运行
        Thread.sleep(2000);
        
        // 模拟数据恶化，触发止损
        System.out.println("\n--- 模拟数据恶化 ---");
        portfolio.setDailyReturn(BigDecimal.valueOf(-0.025)); // -2.5%
        portfolio.setCurrentVolume(80000L); // 成交量增加
        monitoringService.updatePortfolioData(portfolio.getPortfolioId(), portfolio);
        
        // 手动触发检查
        monitoringService.triggerManualCheck(portfolio.getPortfolioId());
        
        Thread.sleep(3000);
        
        // 模拟数据恢复
        System.out.println("\n--- 模拟数据恢复 ---");
        portfolio.setDailyReturn(BigDecimal.valueOf(-0.003)); // -0.3%
        portfolio.setCurrentVolume(25000L); // 成交量正常
        monitoringService.updatePortfolioData(portfolio.getPortfolioId(), portfolio);
        
        // 手动触发检查
        monitoringService.triggerManualCheck(portfolio.getPortfolioId());
        
        Thread.sleep(2000);
        
        // 显示监控状态
        System.out.println("\n--- 监控状态 ---");
        System.out.println(monitoringService.getMonitoringStatus(portfolio.getPortfolioId()));
        
        // 停止监控
        monitoringService.stopMonitoring(portfolio.getPortfolioId());
    }
}
