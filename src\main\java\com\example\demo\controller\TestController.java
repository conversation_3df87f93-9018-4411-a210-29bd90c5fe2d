package com.example.demo.controller;

import com.example.demo.model.ShrAreaSplitResult;
import com.example.demo.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private TestService testService;

    @GetMapping("/area-split")
    public ShrAreaSplitResult testAreaSplit() {
        return testService.testAreaSplit();
    }

    @GetMapping("/performance/{ruleCount}")
    public Map<String, Object> testPerformance(@PathVariable int ruleCount) {
        // 默认使用20万规则，除非指定其他数量
        if (ruleCount <= 0) {
            ruleCount = 200000;
        }

        long executionTime = testService.testPerformanceWithRandomData(ruleCount);

        Map<String, Object> result = new HashMap<>();
        result.put("ruleCount", ruleCount);
        result.put("executionTimeMs", executionTime);
        return result;
    }
}
