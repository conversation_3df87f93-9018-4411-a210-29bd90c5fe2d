package com.example.demo.stoploss.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 止损规则配置
 * 包含条件组合和对应的事件列表
 */
@Data
public class StopLossRule {
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型
     */
    private RuleType ruleType;
    
    /**
     * 适用的组合ID列表
     */
    private List<String> portfolioIds;
    
    /**
     * 止损条件列表
     */
    private List<StopLossCondition> conditions;
    
    /**
     * 条件逻辑关系
     */
    private ConditionLogic conditionLogic;
    
    /**
     * 触发的事件列表
     */
    private List<StopLossEvent> events;
    
    /**
     * 恢复条件列表
     */
    private List<StopLossCondition> recoveryConditions;
    
    /**
     * 恢复条件逻辑关系
     */
    private ConditionLogic recoveryConditionLogic;
    
    /**
     * 恢复事件列表
     */
    private List<StopLossEvent> recoveryEvents;
    
    /**
     * 规则优先级（数字越小优先级越高）
     */
    private int priority;
    
    /**
     * 是否启用
     */
    private boolean enabled;
    
    /**
     * 是否支持恢复
     */
    private boolean recoveryEnabled;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifyTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 规则类型枚举
     */
    public enum RuleType {
        STOP_LOSS("止损规则", "stop_loss"),
        RECOVERY("恢复规则", "recovery"),
        COMBINED("组合规则", "combined");
        
        private final String description;
        private final String code;
        
        RuleType(String description, String code) {
            this.description = description;
            this.code = code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getCode() {
            return code;
        }
    }
    
    /**
     * 条件逻辑关系枚举
     */
    public enum ConditionLogic {
        AND("所有条件都满足", "and"),
        OR("任一条件满足", "or"),
        CUSTOM("自定义逻辑", "custom");
        
        private final String description;
        private final String code;
        
        ConditionLogic(String description, String code) {
            this.description = description;
            this.code = code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getCode() {
            return code;
        }
    }
    
    /**
     * 检查规则是否适用于指定组合
     */
    public boolean isApplicableToPortfolio(String portfolioId) {
        return portfolioIds == null || portfolioIds.isEmpty() || portfolioIds.contains(portfolioId);
    }
    
    /**
     * 获取启用的条件列表
     */
    public List<StopLossCondition> getEnabledConditions() {
        return conditions != null ? 
            conditions.stream().filter(StopLossCondition::isEnabled).toList() : 
            List.of();
    }
    
    /**
     * 获取启用的事件列表
     */
    public List<StopLossEvent> getEnabledEvents() {
        return events != null ? 
            events.stream().filter(StopLossEvent::isEnabled).toList() : 
            List.of();
    }
    
    /**
     * 获取启用的恢复条件列表
     */
    public List<StopLossCondition> getEnabledRecoveryConditions() {
        return recoveryConditions != null ? 
            recoveryConditions.stream().filter(StopLossCondition::isEnabled).toList() : 
            List.of();
    }
    
    /**
     * 获取启用的恢复事件列表
     */
    public List<StopLossEvent> getEnabledRecoveryEvents() {
        return recoveryEvents != null ? 
            recoveryEvents.stream().filter(StopLossEvent::isEnabled).toList() : 
            List.of();
    }
    
    /**
     * 创建简单的止损规则
     */
    public static StopLossRule createSimpleStopLossRule(String ruleId, String ruleName, 
                                                        List<String> portfolioIds,
                                                        List<StopLossCondition> conditions,
                                                        List<StopLossEvent> events) {
        StopLossRule rule = new StopLossRule();
        rule.setRuleId(ruleId);
        rule.setRuleName(ruleName);
        rule.setRuleType(RuleType.STOP_LOSS);
        rule.setPortfolioIds(portfolioIds);
        rule.setConditions(conditions);
        rule.setConditionLogic(ConditionLogic.AND);
        rule.setEvents(events);
        rule.setPriority(1);
        rule.setEnabled(true);
        rule.setRecoveryEnabled(false);
        rule.setCreateTime(LocalDateTime.now());
        rule.setLastModifyTime(LocalDateTime.now());
        return rule;
    }
    
    /**
     * 创建带恢复功能的止损规则
     */
    public static StopLossRule createRecoverableStopLossRule(String ruleId, String ruleName,
                                                           List<String> portfolioIds,
                                                           List<StopLossCondition> conditions,
                                                           List<StopLossEvent> events,
                                                           List<StopLossCondition> recoveryConditions,
                                                           List<StopLossEvent> recoveryEvents) {
        StopLossRule rule = createSimpleStopLossRule(ruleId, ruleName, portfolioIds, conditions, events);
        rule.setRuleType(RuleType.COMBINED);
        rule.setRecoveryConditions(recoveryConditions);
        rule.setRecoveryConditionLogic(ConditionLogic.AND);
        rule.setRecoveryEvents(recoveryEvents);
        rule.setRecoveryEnabled(true);
        return rule;
    }
}
