package com.example.demo.stoploss.handler;

import com.example.demo.stoploss.model.MonitoringContext;
import com.example.demo.stoploss.model.PensionPortfolio;
import com.example.demo.stoploss.model.StopLossEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 通知事件处理器
 * 处理邮件通知、短信通知、系统通知等通知类事件
 */
@Component
public class NotificationEventHandler implements EventHandler {
    
    @Override
    public boolean execute(StopLossEvent event, MonitoringContext context) {
        if (!validateEvent(event)) {
            return false;
        }
        
        try {
            switch (event.getEventType()) {
                case EMAIL_NOTIFICATION:
                    return sendEmailNotification(event, context);
                case SMS_NOTIFICATION:
                    return sendSmsNotification(event, context);
                case SYSTEM_NOTIFICATION:
                    return sendSystemNotification(event, context);
                default:
                    return false;
            }
        } catch (Exception e) {
            System.err.println("通知事件执行失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public StopLossEvent.EventType getSupportedEventType() {
        return StopLossEvent.EventType.EMAIL_NOTIFICATION;
    }
    
    @Override
    public boolean validateEvent(StopLossEvent event) {
        if (event == null || !event.isEnabled()) {
            return false;
        }
        
        StopLossEvent.EventType type = event.getEventType();
        return type == StopLossEvent.EventType.EMAIL_NOTIFICATION ||
               type == StopLossEvent.EventType.SMS_NOTIFICATION ||
               type == StopLossEvent.EventType.SYSTEM_NOTIFICATION;
    }
    
    /**
     * 发送邮件通知
     */
    private boolean sendEmailNotification(StopLossEvent event, MonitoringContext context) {
        String recipient = event.getStringParameter("recipient");
        String subject = event.getStringParameter("subject");
        String content = event.getStringParameter("content");
        
        if (recipient == null || subject == null || content == null) {
            System.err.println("邮件通知参数不完整");
            return false;
        }
        
        // 替换内容中的占位符
        String finalContent = replacePlaceholders(content, context);
        String finalSubject = replacePlaceholders(subject, context);
        
        // 模拟发送邮件
        System.out.println("=== 邮件通知 ===");
        System.out.println("收件人: " + recipient);
        System.out.println("主题: " + finalSubject);
        System.out.println("内容: " + finalContent);
        System.out.println("发送时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("================");
        
        // 在实际实现中，这里应该调用邮件服务
        // emailService.sendEmail(recipient, finalSubject, finalContent);
        
        return true;
    }
    
    /**
     * 发送短信通知
     */
    private boolean sendSmsNotification(StopLossEvent event, MonitoringContext context) {
        String phoneNumber = event.getStringParameter("phoneNumber");
        String message = event.getStringParameter("message");
        
        if (phoneNumber == null || message == null) {
            System.err.println("短信通知参数不完整");
            return false;
        }
        
        // 替换内容中的占位符
        String finalMessage = replacePlaceholders(message, context);
        
        // 模拟发送短信
        System.out.println("=== 短信通知 ===");
        System.out.println("手机号: " + phoneNumber);
        System.out.println("内容: " + finalMessage);
        System.out.println("发送时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("================");
        
        // 在实际实现中，这里应该调用短信服务
        // smsService.sendSms(phoneNumber, finalMessage);
        
        return true;
    }
    
    /**
     * 发送系统通知
     */
    private boolean sendSystemNotification(StopLossEvent event, MonitoringContext context) {
        String userId = event.getStringParameter("userId");
        String title = event.getStringParameter("title");
        String message = event.getStringParameter("message");
        String level = event.getStringParameter("level");
        
        if (userId == null || title == null || message == null) {
            System.err.println("系统通知参数不完整");
            return false;
        }
        
        // 替换内容中的占位符
        String finalTitle = replacePlaceholders(title, context);
        String finalMessage = replacePlaceholders(message, context);
        
        // 模拟发送系统通知
        System.out.println("=== 系统通知 ===");
        System.out.println("用户ID: " + userId);
        System.out.println("标题: " + finalTitle);
        System.out.println("内容: " + finalMessage);
        System.out.println("级别: " + (level != null ? level : "INFO"));
        System.out.println("时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("================");
        
        // 在实际实现中，这里应该调用系统通知服务
        // notificationService.sendNotification(userId, finalTitle, finalMessage, level);
        
        return true;
    }
    
    /**
     * 替换内容中的占位符
     */
    private String replacePlaceholders(String content, MonitoringContext context) {
        if (content == null) {
            return null;
        }
        
        PensionPortfolio portfolio = context.getPortfolio();
        if (portfolio == null) {
            return content;
        }
        
        String result = content;
        
        // 替换组合相关占位符
        result = result.replace("{portfolioId}", portfolio.getPortfolioId() != null ? portfolio.getPortfolioId() : "");
        result = result.replace("{portfolioName}", portfolio.getPortfolioName() != null ? portfolio.getPortfolioName() : "");
        result = result.replace("{currentNetValue}", portfolio.getCurrentNetValue() != null ? portfolio.getCurrentNetValue().toString() : "");
        result = result.replace("{dailyReturn}", portfolio.getDailyReturn() != null ? portfolio.getDailyReturn().toString() : "");
        result = result.replace("{cumulativeReturn}", portfolio.getCumulativeReturn() != null ? portfolio.getCumulativeReturn().toString() : "");
        result = result.replace("{maxDrawdown}", portfolio.getMaxDrawdown() != null ? portfolio.getMaxDrawdown().toString() : "");
        result = result.replace("{stopLossStatus}", portfolio.getStopLossStatus() != null ? portfolio.getStopLossStatus().getDescription() : "");
        
        // 替换时间占位符
        result = result.replace("{currentTime}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result = result.replace("{lastUpdateTime}", portfolio.getLastUpdateTime() != null ? 
                portfolio.getLastUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        
        return result;
    }
    
    @Override
    public String getHandlerDescription() {
        return "通知事件处理器：处理邮件通知、短信通知、系统通知等通知类事件";
    }
}
