package com.example.demo;

import com.example.demo.data.HierarchyData;
import com.example.demo.data.TestData;
import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.tree.RuleDecisionTree;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 反向映射优化的决策树测试类
 */
public class ReverseMappingTreeTest {

    public static void main(String[] args) {
        System.out.println("开始测试反向映射优化的决策树...");
        
        // 初始化层级关系
        Map<String, ArrayList<String>> agencyHierarchy = HierarchyData.initAgencyHierarchy();
        Map<String, ArrayList<String>> regionHierarchy = HierarchyData.initRegionHierarchy();
        
        // 打印层级关系
        printHierarchy("机构层级关系", agencyHierarchy);
        printHierarchy("区域层级关系", regionHierarchy);
        
        // 创建测试规则
        List<AreaSplitStaticRuleDTO> rules = TestData.createTestRules();
        
        // 创建测试数据
        List<ShrAreaSplitDTO> dtos = TestData.createTestDTOs();
        
        // 打印测试规则
        System.out.println("\n测试规则:");
        for (int i = 0; i < rules.size(); i++) {
            AreaSplitStaticRuleDTO rule = rules.get(i);
            System.out.println(i + ": 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                              ", 目标机构=" + rule.getTargetAgency());
        }
        
        // 打印测试数据
        System.out.println("\n测试数据:");
        for (int i = 0; i < dtos.size(); i++) {
            ShrAreaSplitDTO dto = dtos.get(i);
            System.out.println(i + ": 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
            System.out.println("   机构层级: " + agencyHierarchy.get(dto.getSkAgency()));
            System.out.println("   区域层级: " + regionHierarchy.get(dto.getSkRegion()));
        }
        
        // 创建决策树
        List<String> attributeOrder = Arrays.asList(
            "skAgency", "skRegion", "skAccount", "skAccountType",
            "skInvpty", "skInvptyType", "skProduct", "skProductType",
            "dkAgencyType", "dkCustType", "skTradeaccoReg", "agencyno",
            "netno", "dkShareType", "cserialno", "dkTano",
            "skAccountOfAg", "dkBourseflag"
        );
        
        // 测试性能
        System.out.println("\n开始性能测试...");
        
        // 构建决策树并测量时间
        long startBuildTime = System.currentTimeMillis();
        RuleDecisionTree tree = new RuleDecisionTree(rules, attributeOrder);
        long endBuildTime = System.currentTimeMillis();
        System.out.println("决策树构建时间: " + (endBuildTime - startBuildTime) + "ms");
        
        // 测试匹配性能
        System.out.println("\n测试匹配性能:");
        for (ShrAreaSplitDTO dto : dtos) {
            System.out.println("\n数据: 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
            
            // 使用决策树查找匹配规则并测量时间
            long startMatchTime = System.currentTimeMillis();
            List<AreaSplitStaticRuleDTO> matchingRules = tree.findMatchingRules(dto);
            long endMatchTime = System.currentTimeMillis();
            
            System.out.println("匹配时间: " + (endMatchTime - startMatchTime) + "ms");
            System.out.println("匹配规则数量: " + matchingRules.size());
            
            // 打印匹配的规则
            for (AreaSplitStaticRuleDTO rule : matchingRules) {
                System.out.println("规则: 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                                  ", 目标机构=" + rule.getTargetAgency());
            }
        }
        
        System.out.println("\n反向映射优化的决策树测试完成");
    }
    
    /**
     * 打印层级关系
     */
    private static void printHierarchy(String title, Map<String, ArrayList<String>> hierarchy) {
        System.out.println("\n" + title + ":");
        for (Map.Entry<String, ArrayList<String>> entry : hierarchy.entrySet()) {
            System.out.println(entry.getKey() + " -> " + entry.getValue());
        }
    }
}
