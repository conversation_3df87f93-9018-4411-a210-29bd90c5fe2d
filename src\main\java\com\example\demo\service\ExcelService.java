package com.example.demo.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.example.demo.model.CityData;
import com.example.demo.model.CityMapping;
import com.example.demo.model.SelectOption;
import com.example.demo.handler.SelectedSheetWriteHandler;
import com.example.demo.handler.HeaderStyleHandler;
import com.example.demo.model.SelectOption;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ExcelService {

    @Autowired
    private ApplicationContext applicationContext;

    public ByteArrayInputStream generateCityTemplate() {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            // Prepare options map
            Map<String, List<SelectOption>> optionsMap = new HashMap<>();
            optionsMap.put("city", Arrays.asList(
                new SelectOption("101", "北京"),
                new SelectOption("102", "上海"),
                new SelectOption("103", "广州")
            ));

            // 1. First create mapping sheet
            Workbook workbook = WorkbookFactory.create(true);
            Sheet mappingSheet = workbook.createSheet("Sheet2");

            // Write key-value mappings
            List<SelectOption> cityOptions = optionsMap.get("city");
            for (int i = 0; i < cityOptions.size(); i++) {
                Row row = mappingSheet.createRow(i + 1);
                row.createCell(0).setCellValue(cityOptions.get(i).getKey()); // A column - keys
                row.createCell(1).setCellValue(cityOptions.get(i).getValue()); // B column - values
            }

            // 2. Create main sheet with dropdowns
            Sheet mainSheet = workbook.createSheet("Sheet1");

            // 3. Create data validation (dropdown)
            DataValidationHelper dvHelper = mainSheet.getDataValidationHelper();
            DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("Sheet2!$B$2:$B$4");
            CellRangeAddressList addressList = new CellRangeAddressList(1, 100, 2, 2); // C2:C100
            DataValidation validation = dvHelper.createValidation(dvConstraint, addressList);
            mainSheet.addValidationData(validation);

            // 4. Add formula to lookup ID
            Row headerRow = mainSheet.createRow(0);
            headerRow.createCell(2).setCellValue("城市名称");
            headerRow.createCell(3).setCellValue("城市ID");

            // 5. Add sample data
            Row dataRow = mainSheet.createRow(1);
            dataRow.createCell(2).setCellValue("北京"); // Sample city selection

            // Apply formula to all rows in column D
            for (int i = 1; i <= 100; i++) {
                mainSheet.getRow(i).createCell(3).setCellFormula(
                    String.format("INDEX(Sheet2!$A$2:$A$4, MATCH(C%d, Sheet2!$B$2:$B$4, 0))", i+1)
                );
            }

            // 6. Hide the ID column
            mainSheet.setColumnHidden(3, true);

            // 7. Save to byte array
            ByteArrayOutputStream finalOut = new ByteArrayOutputStream();
            workbook.write(finalOut);
            return new ByteArrayInputStream(finalOut.toByteArray());

        } catch (IOException e) {
            throw new RuntimeException("Failed to generate Excel template", e);
        }
    }

    public String importCityData(MultipartFile file) {
        try (InputStream in = file.getInputStream()) {
            StringBuilder result = new StringBuilder();

            EasyExcel.read(in, new ReadListener<CityData>() {
                @Override
                public void invoke(CityData data, AnalysisContext context) {
                    result.append(String.format("城市: %s, ID: %d\n",
                        data.getCityName(), data.getCityId()));
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // Do nothing
                }
            }).sheet().doRead();

            return result.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to import Excel file", e);
        }
    }
}
