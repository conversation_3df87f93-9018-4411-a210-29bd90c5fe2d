package com.example.demo;

import com.example.demo.service.TestService;

/**
 * 优化后的决策树测试类
 */
public class OptimizedTreeTest {

    public static void main(String[] args) {
        // 默认测试20万条规则
        int ruleCount = 200000;
        
        // 如果有命令行参数，使用参数指定的规则数量
        if (args.length > 0) {
            try {
                ruleCount = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.out.println("无效的规则数量参数，使用默认值: " + ruleCount);
            }
        }
        
        System.out.println("开始测试优化后的决策树，规则数量: " + ruleCount);
        
        // 创建TestService实例
        TestService testService = new TestService();
        
        // 先测试结果一致性
        System.out.println("\n测试结果一致性...");
        testService.testResultConsistency();
        
        // 执行性能对比测试
        System.out.println("\n执行性能对比测试...");
        testService.testPerformanceComparison(ruleCount);
    }
}
