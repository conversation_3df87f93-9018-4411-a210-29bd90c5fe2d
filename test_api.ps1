# 止损监控引擎 REST API 测试脚本

Write-Host "=== 养老金组合止损监控引擎 API 测试 ===" -ForegroundColor Green

# 基础URL
$baseUrl = "http://localhost:8080/api/stoploss"

# 测试1: 获取监控引擎状态
Write-Host "`n1. 获取监控引擎状态" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/monitoring/engine/status" -Method GET
    Write-Host "监控引擎状态: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 获取所有规则
Write-Host "`n2. 获取所有规则" -ForegroundColor Yellow
try {
    $rules = Invoke-RestMethod -Uri "$baseUrl/rules" -Method GET
    Write-Host "规则数量: $($rules.Count)" -ForegroundColor Cyan
    foreach ($rule in $rules) {
        Write-Host "  - $($rule.ruleId): $($rule.ruleName)" -ForegroundColor White
    }
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 获取规则统计
Write-Host "`n3. 获取规则统计" -ForegroundColor Yellow
try {
    $stats = Invoke-RestMethod -Uri "$baseUrl/rules/statistics" -Method GET
    Write-Host "规则统计: $($stats | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 创建新的组合数据
Write-Host "`n4. 创建新的组合数据" -ForegroundColor Yellow
$portfolioData = @{
    portfolioId = "API_TEST_001"
    portfolioName = "API测试组合"
    portfolioType = "TEST"
    currentNetValue = 0.9800
    previousNetValue = 1.0000
    totalAssets = 5000000
    dailyReturn = -0.02
    cumulativeReturn = -0.02
    maxDrawdown = 0.02
    currentVolume = 15000
    averageVolume = 10000
    volatility = 0.12
    stopLossStatus = "NORMAL"
    lastUpdateTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/monitoring/start" -Method POST -Body $portfolioData -ContentType "application/json"
    Write-Host "启动监控响应: $response" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 获取监控状态
Write-Host "`n5. 获取监控状态" -ForegroundColor Yellow
try {
    $status = Invoke-RestMethod -Uri "$baseUrl/monitoring/status/API_TEST_001" -Method GET
    Write-Host "监控状态: $($status | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试6: 手动触发检查
Write-Host "`n6. 手动触发检查" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/monitoring/check/API_TEST_001" -Method POST
    Write-Host "触发检查响应: $response" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试7: 更新组合数据（触发止损）
Write-Host "`n7. 更新组合数据（触发止损）" -ForegroundColor Yellow
$updatedPortfolioData = @{
    portfolioId = "API_TEST_001"
    portfolioName = "API测试组合"
    portfolioType = "TEST"
    currentNetValue = 0.9700
    previousNetValue = 1.0000
    totalAssets = 4850000
    dailyReturn = -0.03
    cumulativeReturn = -0.03
    maxDrawdown = 0.03
    currentVolume = 25000
    averageVolume = 10000
    volatility = 0.15
    stopLossStatus = "NORMAL"
    lastUpdateTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/portfolio/API_TEST_001" -Method PUT -Body $updatedPortfolioData -ContentType "application/json"
    Write-Host "更新组合响应: $response" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试8: 再次手动触发检查
Write-Host "`n8. 再次手动触发检查" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/monitoring/check/API_TEST_001" -Method POST
    Write-Host "触发检查响应: $response" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 等待一下让事件处理完成
Start-Sleep -Seconds 2

# 测试9: 获取最终监控状态
Write-Host "`n9. 获取最终监控状态" -ForegroundColor Yellow
try {
    $finalStatus = Invoke-RestMethod -Uri "$baseUrl/monitoring/status/API_TEST_001" -Method GET
    Write-Host "最终监控状态: $($finalStatus | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试10: 停止监控
Write-Host "`n10. 停止监控" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/monitoring/stop/API_TEST_001" -Method POST
    Write-Host "停止监控响应: $response" -ForegroundColor Cyan
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== API 测试完成 ===" -ForegroundColor Green
