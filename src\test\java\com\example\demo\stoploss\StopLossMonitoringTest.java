package com.example.demo.stoploss;

import com.example.demo.stoploss.model.*;
import com.example.demo.stoploss.service.ConditionEvaluationService;
import com.example.demo.stoploss.service.EventExecutionService;
import com.example.demo.stoploss.service.RuleConfigurationService;
import com.example.demo.stoploss.service.StopLossMonitoringService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 止损监控系统测试
 */
@SpringBootTest
public class StopLossMonitoringTest {
    
    @Autowired
    private StopLossMonitoringService monitoringService;
    
    @Autowired
    private RuleConfigurationService ruleConfigurationService;
    
    @Autowired
    private ConditionEvaluationService conditionEvaluationService;
    
    @Autowired
    private EventExecutionService eventExecutionService;
    
    private PensionPortfolio testPortfolio;
    private StopLossRule testRule;
    
    @BeforeEach
    void setUp() {
        // 清理之前的测试数据
        ruleConfigurationService.clearAllRules();
        
        // 创建测试组合
        testPortfolio = createTestPortfolio();
        
        // 创建测试规则
        testRule = createTestRule();
    }
    
    private PensionPortfolio createTestPortfolio() {
        PensionPortfolio portfolio = new PensionPortfolio();
        portfolio.setPortfolioId("TEST_PORTFOLIO");
        portfolio.setPortfolioName("测试组合");
        portfolio.setPortfolioType("TEST");
        portfolio.setCurrentNetValue(BigDecimal.valueOf(1.0000));
        portfolio.setPreviousNetValue(BigDecimal.valueOf(1.0100));
        portfolio.setTotalAssets(BigDecimal.valueOf(1000000));
        portfolio.setDailyReturn(BigDecimal.valueOf(-0.0099)); // -0.99%
        portfolio.setCumulativeReturn(BigDecimal.valueOf(0.00));
        portfolio.setMaxDrawdown(BigDecimal.valueOf(0.01));
        portfolio.setCurrentVolume(10000L);
        portfolio.setAverageVolume(8000L);
        portfolio.setVolatility(BigDecimal.valueOf(0.10));
        portfolio.setStopLossStatus(PensionPortfolio.StopLossStatus.NORMAL);
        portfolio.setLastUpdateTime(LocalDateTime.now());
        return portfolio;
    }
    
    private StopLossRule createTestRule() {
        // 创建条件：日收益率低于-1%
        StopLossCondition condition = StopLossCondition.createDailyReturnLossCondition(
                "TEST_CONDITION", BigDecimal.valueOf(-1.0));
        
        // 创建事件：发送通知
        StopLossEvent event = StopLossEvent.createEmailNotificationEvent(
                "TEST_EVENT", "<EMAIL>", 
                "测试通知", "这是一个测试通知");
        
        // 创建恢复条件：日收益率高于-0.5%
        StopLossCondition recoveryCondition = new StopLossCondition();
        recoveryCondition.setConditionId("RECOVERY_CONDITION");
        recoveryCondition.setConditionName("恢复条件");
        recoveryCondition.setConditionType(StopLossCondition.ConditionType.DAILY_RETURN_LOSS);
        recoveryCondition.setParameter("threshold", BigDecimal.valueOf(-0.5));
        recoveryCondition.setEnabled(true);
        
        // 创建恢复事件
        StopLossEvent recoveryEvent = StopLossEvent.createResumeTradingEvent(
                "RECOVERY_EVENT", "TEST_PORTFOLIO");
        
        return StopLossRule.createRecoverableStopLossRule(
                "TEST_RULE", "测试规则",
                Arrays.asList("TEST_PORTFOLIO"),
                Arrays.asList(condition),
                Arrays.asList(event),
                Arrays.asList(recoveryCondition),
                Arrays.asList(recoveryEvent));
    }
    
    @Test
    void testRuleConfiguration() {
        // 测试添加规则
        ruleConfigurationService.addRule(testRule);
        
        // 验证规则已添加
        StopLossRule retrievedRule = ruleConfigurationService.getRule("TEST_RULE");
        assertNotNull(retrievedRule);
        assertEquals("TEST_RULE", retrievedRule.getRuleId());
        assertEquals("测试规则", retrievedRule.getRuleName());
        
        // 测试获取适用规则
        List<StopLossRule> applicableRules = ruleConfigurationService.getApplicableRules("TEST_PORTFOLIO");
        assertEquals(1, applicableRules.size());
        assertEquals("TEST_RULE", applicableRules.get(0).getRuleId());
    }
    
    @Test
    void testConditionEvaluation() {
        // 创建监控上下文
        MonitoringContext context = new MonitoringContext("TEST_CONTEXT", testPortfolio);
        
        // 测试条件评估
        StopLossCondition condition = testRule.getConditions().get(0);
        boolean result = conditionEvaluationService.evaluateCondition(condition, context);
        
        // 日收益率为-0.99%，应该满足-1%的条件
        assertTrue(result);
        
        // 修改组合数据，使条件不满足
        testPortfolio.setDailyReturn(BigDecimal.valueOf(-0.005)); // -0.5%
        context.setPortfolio(testPortfolio);
        
        result = conditionEvaluationService.evaluateCondition(condition, context);
        assertFalse(result);
    }
    
    @Test
    void testEventExecution() {
        // 创建监控上下文
        MonitoringContext context = new MonitoringContext("TEST_CONTEXT", testPortfolio);
        
        // 测试事件执行
        StopLossEvent event = testRule.getEvents().get(0);
        boolean result = eventExecutionService.executeEvent(event, context);
        
        assertTrue(result);
        assertTrue(context.isEventExecuted("TEST_EVENT"));
    }
    
    @Test
    void testMonitoringLifecycle() throws InterruptedException {
        // 添加规则
        ruleConfigurationService.addRule(testRule);
        
        // 启动监控
        monitoringService.startMonitoring(testPortfolio);
        
        // 获取监控状态
        Map<String, Object> status = monitoringService.getMonitoringStatus("TEST_PORTFOLIO");
        assertNotNull(status);
        assertEquals("监控中", status.get("status"));
        
        // 等待一段时间让监控运行
        Thread.sleep(1000);
        
        // 暂停监控
        monitoringService.pauseMonitoring("TEST_PORTFOLIO");
        status = monitoringService.getMonitoringStatus("TEST_PORTFOLIO");
        assertEquals("暂停", status.get("status"));
        
        // 恢复监控
        monitoringService.resumeMonitoring("TEST_PORTFOLIO");
        status = monitoringService.getMonitoringStatus("TEST_PORTFOLIO");
        assertEquals("监控中", status.get("status"));
        
        // 停止监控
        monitoringService.stopMonitoring("TEST_PORTFOLIO");
        status = monitoringService.getMonitoringStatus("TEST_PORTFOLIO");
        assertEquals("未监控", status.get("status"));
    }
    
    @Test
    void testStopLossAndRecovery() throws InterruptedException {
        // 添加规则
        ruleConfigurationService.addRule(testRule);
        
        // 设置组合数据触发止损
        testPortfolio.setDailyReturn(BigDecimal.valueOf(-0.015)); // -1.5%
        
        // 启动监控
        monitoringService.startMonitoring(testPortfolio);
        
        // 手动触发检查
        monitoringService.triggerManualCheck("TEST_PORTFOLIO");
        
        // 等待处理
        Thread.sleep(500);
        
        // 验证止损状态
        // 注意：在实际实现中，这里应该检查组合状态是否已更新
        
        // 模拟数据恢复
        testPortfolio.setDailyReturn(BigDecimal.valueOf(-0.003)); // -0.3%
        testPortfolio.setStopLossStatus(PensionPortfolio.StopLossStatus.STOP_LOSS_TRIGGERED);
        monitoringService.updatePortfolioData("TEST_PORTFOLIO", testPortfolio);
        
        // 手动触发检查
        monitoringService.triggerManualCheck("TEST_PORTFOLIO");
        
        // 等待处理
        Thread.sleep(500);
        
        // 停止监控
        monitoringService.stopMonitoring("TEST_PORTFOLIO");
    }
    
    @Test
    void testRuleValidation() {
        // 测试有效规则
        Map<String, String> validationResults = ruleConfigurationService.validateRule(testRule);
        assertTrue(validationResults.isEmpty());
        
        // 测试无效规则
        StopLossRule invalidRule = new StopLossRule();
        invalidRule.setRuleId(""); // 空ID
        invalidRule.setRuleName(""); // 空名称
        
        validationResults = ruleConfigurationService.validateRule(invalidRule);
        assertFalse(validationResults.isEmpty());
        assertTrue(validationResults.containsKey("ruleId"));
        assertTrue(validationResults.containsKey("ruleName"));
        assertTrue(validationResults.containsKey("conditions"));
        assertTrue(validationResults.containsKey("events"));
    }
    
    @Test
    void testRuleStatistics() {
        // 添加多个规则
        ruleConfigurationService.addRule(testRule);
        
        StopLossRule anotherRule = createTestRule();
        anotherRule.setRuleId("ANOTHER_RULE");
        anotherRule.setRuleName("另一个规则");
        anotherRule.setEnabled(false);
        ruleConfigurationService.addRule(anotherRule);
        
        // 获取统计信息
        Map<String, Object> statistics = ruleConfigurationService.getRuleStatistics();
        
        assertEquals(2L, statistics.get("totalRules"));
        assertEquals(1L, statistics.get("enabledRules"));
        assertEquals(1L, statistics.get("disabledRules"));
    }
}
