package com.example.demo;

import com.example.demo.data.HierarchyData;
import com.example.demo.data.TestData;
import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.tree.RuleDecisionTree;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 决策树层级匹配测试类
 */
public class TreeHierarchyTest {

    public static void main(String[] args) {
        System.out.println("开始测试决策树层级匹配...");
        
        // 初始化层级关系
        Map<String, ArrayList<String>> agencyHierarchy = HierarchyData.initAgencyHierarchy();
        Map<String, ArrayList<String>> regionHierarchy = HierarchyData.initRegionHierarchy();
        
        // 创建测试规则
        List<AreaSplitStaticRuleDTO> rules = TestData.createTestRules();
        
        // 创建测试数据
        List<ShrAreaSplitDTO> dtos = TestData.createTestDTOs();
        
        // 打印测试规则
        System.out.println("\n测试规则:");
        for (int i = 0; i < rules.size(); i++) {
            AreaSplitStaticRuleDTO rule = rules.get(i);
            System.out.println(i + ": 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                              ", 目标机构=" + rule.getTargetAgency());
        }
        
        // 打印测试数据
        System.out.println("\n测试数据:");
        for (int i = 0; i < dtos.size(); i++) {
            ShrAreaSplitDTO dto = dtos.get(i);
            System.out.println(i + ": 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
        }
        
        // 创建决策树
        List<String> attributeOrder = Arrays.asList(
            "skAgency", "skRegion", "skAccount", "skAccountType",
            "skInvpty", "skInvptyType", "skProduct", "skProductType",
            "dkAgencyType", "dkCustType", "skTradeaccoReg", "agencyno",
            "netno", "dkShareType", "cserialno", "dkTano",
            "skAccountOfAg", "dkBourseflag"
        );
        RuleDecisionTree tree = new RuleDecisionTree(rules, attributeOrder);
        
        // 测试匹配
        System.out.println("\n决策树匹配结果:");
        for (ShrAreaSplitDTO dto : dtos) {
            System.out.println("\n数据: 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
            
            // 使用决策树查找匹配规则
            List<AreaSplitStaticRuleDTO> matchingRules = tree.findMatchingRules(dto);
            
            System.out.println("匹配规则数量: " + matchingRules.size());
            
            // 打印匹配的规则
            for (AreaSplitStaticRuleDTO rule : matchingRules) {
                System.out.println("规则: 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                                  ", 目标机构=" + rule.getTargetAgency());
            }
        }
        
        System.out.println("\n决策树层级匹配测试完成");
    }
}
