package com.example.demo.service;

import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.model.ShrAreaSplitResult;
import com.example.demo.tree.RuleDecisionTree;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

@Service
public class TestService {

    // 机构层级关系
    private Map<String, ArrayList<String>> agency_hierarchy = new HashMap<>();
    // 区域层级关系
    private Map<String, ArrayList<String>> region_hierarchy = new HashMap<>();
    // 区域拆分静态规则
    private List<AreaSplitStaticRuleDTO> areaSplitStaticRule = new ArrayList<>();

    // 决策树相关
    private RuleDecisionTree ruleDecisionTree;
    private boolean isTreeBuilt = false;
    private List<String> attributeOrder = Arrays.asList(
        "skAgency", "skRegion", "skAccount", "skAccountType",
        "skInvpty", "skInvptyType", "skProduct", "skProductType",
        "dkAgencyType", "dkCustType", "skTradeaccoReg", "agencyno",
        "netno", "dkShareType", "cserialno", "dkTano",
        "skAccountOfAg", "dkBourseflag"
    );

    /**
     * 构建决策树 - 优化版
     */
    private void buildDecisionTree() {
        // 如果树已经构建，直接返回
        if (isTreeBuilt) return;

        // 构建决策树
        long startTime = System.currentTimeMillis();

        // 使用优化后的决策树类
        ruleDecisionTree = new RuleDecisionTree(areaSplitStaticRule, attributeOrder);

        long endTime = System.currentTimeMillis();
        long buildTime = endTime - startTime;

        // 输出构建信息
        System.out.println("决策树构建完成，耗时: " + buildTime + "ms");
        System.out.println("规则总数: " + ruleDecisionTree.getRuleCount());
        System.out.println("平均每条规则构建时间: " +
                          String.format("%.3f", (double)buildTime / ruleDecisionTree.getRuleCount()) + "ms");

        // 标记树已经构建
        isTreeBuilt = true;
    }

    /**
     * 获取当前分享拆分结果 - 原始版本
     * @param shrAreaSplitDTO 拆分输入参数
     * @return 拆分结果列表
     */
    private ArrayList<ShrAreaSplitResult> getCurrentShareSplitResult(ShrAreaSplitDTO shrAreaSplitDTO) {
        // 1. 预先创建结果集合，使用合适的初始容量
        ArrayList<ShrAreaSplitResult> result = new ArrayList<>(16);

        // 2. 预先获取所有需要的数据，避免重复获取
        ArrayList<String> sk_agencyList = agency_hierarchy.getOrDefault(shrAreaSplitDTO.getSkAgency(), new ArrayList<>());
        ArrayList<String> sk_regionList = region_hierarchy.getOrDefault(shrAreaSplitDTO.getSkRegion(), new ArrayList<>());

        // 3. 缓存DTO中的常用值，避免重复访问
        final String dtoSkAccount = shrAreaSplitDTO.getSkAccount();
        final String dtoSkAccountType = shrAreaSplitDTO.getSkAccountType();
        final String dtoSkInvPty = shrAreaSplitDTO.getSkInvPty();
        final String dtoSkInvPtyType = shrAreaSplitDTO.getSkInvPtyType();
        final String dtoSkProduct = shrAreaSplitDTO.getSkProduct();
        final String dtoSkProdType = shrAreaSplitDTO.getSkProdType();
        final String dtoDkAgencyType = shrAreaSplitDTO.getDkAgencyType();
        final String dtoDkCustType = shrAreaSplitDTO.getDkCustType();
        final String dtoSkTradeAccoReg = shrAreaSplitDTO.getSkTradeAccoReg();
        final String dtoAgencyNo = shrAreaSplitDTO.getAgencyNo();
        final String dtoNetNo = shrAreaSplitDTO.getNetNo();
        final String dtoDkShareType = shrAreaSplitDTO.getDkShareType();
        final String dtoTrdShrTrxSerialNo = shrAreaSplitDTO.getTrdShrTrxSerialNo();
        final String dtoDkTano = shrAreaSplitDTO.getDkTano();
        final String dtoSkAccountOfAg = shrAreaSplitDTO.getSkAccountOfAg();
        final String dtoDkBourseFlag = shrAreaSplitDTO.getDkBourseFlag();
        final String dtoEffectiveFrom = shrAreaSplitDTO.getEffectiveFrom();
        final String dtoEffectiveTo = shrAreaSplitDTO.getEffectiveTo();

        // 4. 预先创建常量，避免重复创建
        final String WILDCARD = "*";
        final String DEFAULT = "-1";
        final BigDecimal ONE = new BigDecimal("1.0000");
        final String ZERO = "0.0000";

        // 5. 使用更高效的数据结构
        BigDecimal ratio = ONE;
        List<String[]> timeSlice = new ArrayList<>(8); // 使用合适的初始容量
        String[] data = {dtoEffectiveFrom, dtoEffectiveTo};
        timeSlice.add(data);

        // 7. 使用索引遍历，比迭代器更高效
        for (AreaSplitStaticRuleDTO ruleDTO : areaSplitStaticRule) {
            // 8. 使用短路逻辑，将最可能过滤的条件放在前面
            // 9. 合并条件检查，减少方法调用
            if (!DEFAULT.equals(ruleDTO.getSkAgency()) && !sk_agencyList.contains(ruleDTO.getSkAgency())) continue;
            if (!DEFAULT.equals(ruleDTO.getSkRegion()) && !sk_regionList.contains(ruleDTO.getSkRegion())) continue;

            // 10. 优化字符串比较，使用equals而不是==，并且避免不必要的比较
            String ruleSkAccount = ruleDTO.getSkAccount();
            if (!DEFAULT.equals(ruleSkAccount) && !ruleSkAccount.equals(dtoSkAccount)) continue;

            String ruleSkAccountType = ruleDTO.getSkAccountType();
            if (!DEFAULT.equals(ruleSkAccountType) && !ruleSkAccountType.equals(dtoSkAccountType)) continue;

            String ruleSkInvpty = ruleDTO.getSkInvpty();
            if (!DEFAULT.equals(ruleSkInvpty) && !ruleSkInvpty.equals(dtoSkInvPty)) continue;

            String ruleSkInvptyType = ruleDTO.getSkInvptyType();
            if (!DEFAULT.equals(ruleSkInvptyType) && !ruleSkInvptyType.equals(dtoSkInvPtyType)) continue;

            String ruleSkProduct = ruleDTO.getSkProduct();
            if (!DEFAULT.equals(ruleSkProduct) && !ruleSkProduct.equals(dtoSkProduct)) continue;

            String ruleSkProductType = ruleDTO.getSkProductType();
            if (!DEFAULT.equals(ruleSkProductType) && !ruleSkProductType.equals(dtoSkProdType)) continue;

            String ruleDkAgencyType = ruleDTO.getDkAgencyType();
            if (!WILDCARD.equals(ruleDkAgencyType) && !ruleDkAgencyType.equals(dtoDkAgencyType)) continue;

            String ruleDkCustType = ruleDTO.getDkCustType();
            if (!WILDCARD.equals(ruleDkCustType) && !ruleDkCustType.equals(dtoDkCustType)) continue;

            String ruleSkTradeaccoReg = ruleDTO.getSkTradeaccoReg();
            if (!DEFAULT.equals(ruleSkTradeaccoReg) && !ruleSkTradeaccoReg.equals(dtoSkTradeAccoReg)) continue;

            String ruleAgencyno = ruleDTO.getAgencyno();
            if (!WILDCARD.equals(ruleAgencyno) && !ruleAgencyno.equals(dtoAgencyNo)) continue;

            String ruleNetno = ruleDTO.getNetno();
            if (!WILDCARD.equals(ruleNetno) && !ruleNetno.equals(dtoNetNo)) continue;

            String ruleDkShareType = ruleDTO.getDkShareType();
            if (!WILDCARD.equals(ruleDkShareType) && !ruleDkShareType.equals(dtoDkShareType)) continue;

            String ruleCserialno = ruleDTO.getCserialno();
            if (!WILDCARD.equals(ruleCserialno) && !ruleCserialno.equals(dtoTrdShrTrxSerialNo)) continue;

            String ruleDkTano = ruleDTO.getDkTano();
            if (!WILDCARD.equals(ruleDkTano) && !ruleDkTano.equals(dtoDkTano)) continue;

            String ruleSkAccountOfAg = ruleDTO.getSkAccountOfAg();
            if (!DEFAULT.equals(ruleSkAccountOfAg) && !ruleSkAccountOfAg.equals(dtoSkAccountOfAg)) continue;

            String ruleDkBourseflag = ruleDTO.getDkBourseflag();
            if (!WILDCARD.equals(ruleDkBourseflag) && !ruleDkBourseflag.equals(dtoDkBourseFlag)) continue;

            // 11. 预先解析日期，避免重复解析
            int rule_from = Integer.parseInt(ruleDTO.getEffectiveFrom());
            int rule_to = Integer.parseInt(ruleDTO.getEffectiveTo());

            // 12. 优化数据结构的创建和使用
            String[] current = new String[2];
            List<String[]> effective_data = new ArrayList<>(timeSlice.size() + 2);
            effective_data.addAll(timeSlice);
            List<String[]> currents = new ArrayList<>(4);

            // 13. 使用局部变量缓存循环中的值
            int timeSliceSize = timeSlice.size();
            for (String[] resource : timeSlice) {
                int data_from = Integer.parseInt(resource[0]);
                int data_to = Integer.parseInt(resource[1]);

                boolean flag = false;

                // 14. 优化条件判断，减少重复计算
                if (rule_from <= data_from) {
                    if (data_from < rule_to) {
                        if (rule_to < data_to) {
                            // 情况1: rule_from <= data_from < rule_to < data_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(rule_to), Integer.toString(data_to)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        } else {
                            // 情况2: rule_from <= data_from < data_to <= rule_to
                            flag = true;
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                            if (!containsArray(currents, current)) {
                                currents.add(current);
                            }
                        }
                    }
                } else { // data_from < rule_from
                    if (rule_from < data_to) {
                        if (data_to <= rule_to) {
                            // 情况3: data_from < rule_from < data_to <= rule_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(data_from), Integer.toString(rule_from)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        } else { // rule_to < data_to
                            // 情况4: data_from < rule_from < rule_to < data_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(data_from), Integer.toString(rule_from)});
                            effective_data.add(new String[]{Integer.toString(rule_to), Integer.toString(data_to)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        }
                    }
                }

                // 15. 优化BigDecimal计算
                String ruleRatio = ruleDTO.getRatio();
                if (flag && ratio.subtract(new BigDecimal(ruleRatio)).compareTo(BigDecimal.ZERO) == 0) {
                    effective_data.remove(resource);
                }
            }

            // 16. 更新timeSlice引用而不是创建新集合
            timeSlice = effective_data;

            // 17. 优化BigDecimal计算
            ratio = ratio.subtract(new BigDecimal(ruleDTO.getRatio()));

            // 18. 优化结果处理
            if (currents.size() > 1) {
                for (String[] curr : currents) {
                    fillShrAreaSplitResult(result, shrAreaSplitDTO, ruleDTO, curr);
                }
            } else {
                fillShrAreaSplitResult(result, shrAreaSplitDTO, ruleDTO, current);
            }

            // 19. 提前返回，避免不必要的计算
            if (timeSlice.isEmpty()) {
                return result;
            }

            // 20. 使用常量比较，避免创建新对象
            if (ratio.compareTo(BigDecimal.ZERO) == 0) {
                ratio = ONE;
            }
        }

        return result;
    }

    /**
     * 检查数组列表中是否包含指定数组
     * 优化版本，避免使用stream操作
     */
    private boolean containsArray(List<String[]> list, String[] target) {
        for (String[] arr : list) {
            if (Arrays.equals(arr, target)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算时间切片 - 优化版
     * @param dataFrom 数据开始时间
     * @param dataTo 数据结束时间
     * @param ruleFrom 规则开始时间
     * @param ruleTo 规则结束时间
     * @return 时间切片数组
     */
    private String[] currentTimeSlice(int dataFrom, int dataTo, int ruleFrom, int ruleTo) {
        // 使用三元运算符简化逻辑
        int start = Math.max(dataFrom, ruleFrom);
        int end = Math.min(dataTo, ruleTo);

        // 直接使用Integer.toString而不是字符串连接
        return new String[] {
            Integer.toString(start),
            Integer.toString(end)
        };
    }

    /**
     * 填充拆分结果 - 优化版
     * @param result 结果列表
     * @param shrAreaSplitDTO 输入DTO
     * @param ruleDTO 规则DTO
     * @param current 当前时间切片
     */
    private void fillShrAreaSplitResult(ArrayList<ShrAreaSplitResult> result, ShrAreaSplitDTO shrAreaSplitDTO,
                                      AreaSplitStaticRuleDTO ruleDTO, String[] current) {
        // 使用对象池模式或预分配对象可以进一步提高性能
        ShrAreaSplitResult splitResult = new ShrAreaSplitResult();

        // 使用批量设置而不是多次调用setter可以提高性能
        // 如果有大量字段，可以考虑使用BeanUtils或其他工具批量复制

        // 1. 复制原始数据属性 - 使用缓存的属性值而不是重复调用getter
        splitResult.setSkAgency(shrAreaSplitDTO.getSkAgency());
        splitResult.setSkRegion(shrAreaSplitDTO.getSkRegion());
        splitResult.setSkAccount(shrAreaSplitDTO.getSkAccount());
        splitResult.setSkAccountType(shrAreaSplitDTO.getSkAccountType());
        splitResult.setSkInvPty(shrAreaSplitDTO.getSkInvPty());
        splitResult.setSkInvPtyType(shrAreaSplitDTO.getSkInvPtyType());
        splitResult.setSkProduct(shrAreaSplitDTO.getSkProduct());
        splitResult.setSkProdType(shrAreaSplitDTO.getSkProdType());
        splitResult.setDkAgencyType(shrAreaSplitDTO.getDkAgencyType());
        splitResult.setDkCustType(shrAreaSplitDTO.getDkCustType());
        splitResult.setSkTradeAccoReg(shrAreaSplitDTO.getSkTradeAccoReg());
        splitResult.setAgencyNo(shrAreaSplitDTO.getAgencyNo());
        splitResult.setNetNo(shrAreaSplitDTO.getNetNo());
        splitResult.setDkShareType(shrAreaSplitDTO.getDkShareType());
        splitResult.setTrdShrTrxSerialNo(shrAreaSplitDTO.getTrdShrTrxSerialNo());
        splitResult.setDkTano(shrAreaSplitDTO.getDkTano());
        splitResult.setSkAccountOfAg(shrAreaSplitDTO.getSkAccountOfAg());
        splitResult.setDkBourseFlag(shrAreaSplitDTO.getDkBourseFlag());

        // 2. 设置拆分结果特有属性 - 直接访问数组元素
        splitResult.setEffectiveFrom(current[0]);
        splitResult.setEffectiveTo(current[1]);
        splitResult.setRatio(ruleDTO.getRatio());
        splitResult.setTargetAgency(ruleDTO.getTargetAgency());
        splitResult.setTargetRegion(ruleDTO.getTargetRegion());

        // 3. 直接添加到结果列表
        result.add(splitResult);

        // 注意: 如果需要进一步提高性能，可以考虑使用对象池模式或批量复制工具
    }

    // 初始化数据方法
    public void initData() {
        // 初始化机构层级关系
        ArrayList<String> bj_agencies = new ArrayList<>();
        bj_agencies.add("BJ001");
        bj_agencies.add("BJ002");
        agency_hierarchy.put("BJ", bj_agencies);

        ArrayList<String> sh_agencies = new ArrayList<>();
        sh_agencies.add("SH001");
        sh_agencies.add("SH002");
        agency_hierarchy.put("SH", sh_agencies);

        // 初始化区域层级关系
        ArrayList<String> north_regions = new ArrayList<>();
        north_regions.add("BJ");
        north_regions.add("TJ");
        region_hierarchy.put("NORTH", north_regions);

        ArrayList<String> east_regions = new ArrayList<>();
        east_regions.add("SH");
        east_regions.add("JS");
        region_hierarchy.put("EAST", east_regions);

        // 初始化拆分规则
        AreaSplitStaticRuleDTO rule1 = new AreaSplitStaticRuleDTO();
        rule1.setSkAgency("BJ");
        rule1.setSkRegion("-1");
        rule1.setSkAccount("-1");
        rule1.setSkAccountType("-1");
        rule1.setSkInvpty("-1");
        rule1.setSkInvptyType("-1");
        rule1.setSkProduct("-1");
        rule1.setSkProductType("-1");
        rule1.setDkAgencyType("*");
        rule1.setDkCustType("*");
        rule1.setSkTradeaccoReg("-1");
        rule1.setAgencyno("*");
        rule1.setNetno("*");
        rule1.setDkShareType("*");
        rule1.setCserialno("*");
        rule1.setDkTano("*");
        rule1.setSkAccountOfAg("-1");
        rule1.setDkBourseflag("*");
        rule1.setEffectiveFrom("********");
        rule1.setEffectiveTo("********");
        rule1.setRatio("0.6000");
        rule1.setTargetAgency("BJ001");
        rule1.setTargetRegion("BJ");

        AreaSplitStaticRuleDTO rule2 = new AreaSplitStaticRuleDTO();
        rule2.setSkAgency("BJ");
        rule2.setSkRegion("-1");
        rule2.setSkAccount("-1");
        rule2.setSkAccountType("-1");
        rule2.setSkInvpty("-1");
        rule2.setSkInvptyType("-1");
        rule2.setSkProduct("-1");
        rule2.setSkProductType("-1");
        rule2.setDkAgencyType("*");
        rule2.setDkCustType("*");
        rule2.setSkTradeaccoReg("-1");
        rule2.setAgencyno("*");
        rule2.setNetno("*");
        rule2.setDkShareType("*");
        rule2.setCserialno("*");
        rule2.setDkTano("*");
        rule2.setSkAccountOfAg("-1");
        rule2.setDkBourseflag("*");
        rule2.setEffectiveFrom("********");
        rule2.setEffectiveTo("********");
        rule2.setRatio("0.4000");
        rule2.setTargetAgency("BJ002");
        rule2.setTargetRegion("BJ");

        areaSplitStaticRule.add(rule1);
        areaSplitStaticRule.add(rule2);

        // 构建决策树
        isTreeBuilt = false; // 重置决策树状态
        buildDecisionTree();
    }

    // 测试方法
    public ShrAreaSplitResult testAreaSplit() {
        // 初始化数据
        initData();

        // 创建测试数据
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");

        // 执行拆分 - 使用决策树优化版本
        ArrayList<ShrAreaSplitResult> results = getCurrentShareSplitResultWithTree(testDTO);

        // 返回第一个结果作为测试
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 生成随机的区域拆分规则
     */
    private AreaSplitStaticRuleDTO generateRandomRule() {
        AreaSplitStaticRuleDTO rule = new AreaSplitStaticRuleDTO();

        // 随机生成机构和区域
        String[] agencies = {"BJ", "SH", "GZ", "SZ", "-1"};
        String[] regions = {"BJ", "SH", "GZ", "SZ", "-1"};
        String[] accounts = {"ACC001", "ACC002", "ACC003", "-1"};
        String[] types = {"1", "2", "3", "-1"};
        String[] wildcards = {"*"};

        // 随机选择值
        int agencyIndex = (int) (Math.random() * agencies.length);
        int regionIndex = (int) (Math.random() * regions.length);
        int accountIndex = (int) (Math.random() * accounts.length);
        int typeIndex = (int) (Math.random() * types.length);

        rule.setSkAgency(agencies[agencyIndex]);
        rule.setSkRegion(regions[regionIndex]);
        rule.setSkAccount(accounts[accountIndex]);
        rule.setSkAccountType(types[typeIndex]);
        rule.setSkInvpty(accounts[(int) (Math.random() * accounts.length)]);
        rule.setSkInvptyType(types[(int) (Math.random() * types.length)]);
        rule.setSkProduct(accounts[(int) (Math.random() * accounts.length)]);
        rule.setSkProductType(types[(int) (Math.random() * types.length)]);
        rule.setDkAgencyType(wildcards[0]);
        rule.setDkCustType(wildcards[0]);
        rule.setSkTradeaccoReg(accounts[(int) (Math.random() * accounts.length)]);
        rule.setAgencyno(wildcards[0]);
        rule.setNetno(wildcards[0]);
        rule.setDkShareType(wildcards[0]);
        rule.setCserialno(wildcards[0]);
        rule.setDkTano(wildcards[0]);
        rule.setSkAccountOfAg(accounts[(int) (Math.random() * accounts.length)]);
        rule.setDkBourseflag(wildcards[0]);

        // 设置有效期
        int year = 2023;
        int startMonth = (int) (Math.random() * 12) + 1;
        int endMonth = startMonth + (int) (Math.random() * (12 - startMonth + 1));
        String startDate = String.format("%d%02d01", year, startMonth);
        String endDate = String.format("%d%02d28", year, endMonth);
        rule.setEffectiveFrom(startDate);
        rule.setEffectiveTo(endDate);

        // 设置比例
        double ratio = Math.random() * 0.5; // 0到0.5之间的随机比例
        rule.setRatio(String.format("%.4f", ratio));

        // 设置目标机构和区域
        rule.setTargetAgency(agencies[agencyIndex] + "001");
        rule.setTargetRegion(regions[regionIndex]);

        return rule;
    }

    /**
     * 生成随机测试数据并测试性能
     * @param ruleCount 规则数量
     * @return 执行时间（毫秒）
     */
    public long testPerformanceWithRandomData(int ruleCount) {
        // 清空现有规则
        areaSplitStaticRule.clear();

        // 生成随机规则
        System.out.println("生成" + ruleCount + "条随机规则...");
        long startGeneration = System.currentTimeMillis();
        for (int i = 0; i < ruleCount; i++) {
            areaSplitStaticRule.add(generateRandomRule());
        }
        long endGeneration = System.currentTimeMillis();
        System.out.println("规则生成完成，耗时: " + (endGeneration - startGeneration) + "ms");

        // 初始化层级关系
        initHierarchyData();

        // 创建测试数据
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");

        // 执行拆分并计时
        System.out.println("开始执行getCurrentShareSplitResult方法...");
        long startTime = System.currentTimeMillis();
        ArrayList<ShrAreaSplitResult> results = getCurrentShareSplitResult(testDTO);
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        System.out.println("执行完成，耗时: " + executionTime + "ms");
        System.out.println("结果数量: " + results.size());

        return executionTime;
    }

    /**
     * 初始化层级关系数据
     * 使用外部数据源初始化层级关系
     */
    private void initHierarchyData() {
        // 清空现有数据
        agency_hierarchy.clear();
        region_hierarchy.clear();

        // 使用外部数据源初始化层级关系
        agency_hierarchy.putAll(com.example.demo.data.HierarchyData.initAgencyHierarchy());
        region_hierarchy.putAll(com.example.demo.data.HierarchyData.initRegionHierarchy());
    }

    /**
     * 使用决策树的获取当前分享拆分结果
     * @param shrAreaSplitDTO 拆分输入参数
     * @return 拆分结果列表
     */
    private ArrayList<ShrAreaSplitResult> getCurrentShareSplitResultWithTree(ShrAreaSplitDTO shrAreaSplitDTO) {
        // 确保决策树已构建
        if (!isTreeBuilt) {
            buildDecisionTree();
        }

        // 使用决策树查找匹配规则
        long startMatchTime = System.currentTimeMillis();
        List<AreaSplitStaticRuleDTO> matchingRules = ruleDecisionTree.findMatchingRules(shrAreaSplitDTO);
        long endMatchTime = System.currentTimeMillis();

        if (matchingRules.isEmpty()) {
            return new ArrayList<>();
        }

        System.out.println("决策树匹配完成，耗时: " + (endMatchTime - startMatchTime) + "ms");
        System.out.println("匹配规则数量: " + matchingRules.size());

        // 创建结果集合
        ArrayList<ShrAreaSplitResult> result = new ArrayList<>(16);

        // 获取机构和区域层级关系 - 与原始方法保持一致
        ArrayList<String> sk_agencyList = agency_hierarchy.getOrDefault(shrAreaSplitDTO.getSkAgency(), new ArrayList<>());
        ArrayList<String> sk_regionList = region_hierarchy.getOrDefault(shrAreaSplitDTO.getSkRegion(), new ArrayList<>());

        // 缓存常用值
        final String dtoEffectiveFrom = shrAreaSplitDTO.getEffectiveFrom();
        final String dtoEffectiveTo = shrAreaSplitDTO.getEffectiveTo();
        final String DEFAULT = "-1";
        final String WILDCARD = "*";

        // 初始化时间切片和比例
        BigDecimal ratio = new BigDecimal("1.0000");
        List<String[]> timeSlice = new ArrayList<>(4);
        timeSlice.add(new String[]{dtoEffectiveFrom, dtoEffectiveTo});

        // 处理匹配的规则 - 与原始方法保持一致的处理逻辑
        for (AreaSplitStaticRuleDTO ruleDTO : matchingRules) {
            // 再次验证规则是否匹配 - 规则适用于上层，数据来自下层
            // 1. 机构匹配逻辑
            if (!DEFAULT.equals(ruleDTO.getSkAgency())) {
                // 如果规则机构不是默认值，需要检查规则机构是否在数据机构的层级列表中
                boolean agencyMatch = false;

                // 获取数据机构的层级列表
                ArrayList<String> agencyHierarchy = agency_hierarchy.get(shrAreaSplitDTO.getSkAgency());

                // 如果数据机构有层级列表，检查规则机构是否在其中
                if (agencyHierarchy != null && agencyHierarchy.contains(ruleDTO.getSkAgency())) {
                    agencyMatch = true;
                }
                // 如果数据机构没有层级列表，只有直接匹配才算匹配成功
                else if (ruleDTO.getSkAgency().equals(shrAreaSplitDTO.getSkAgency())) {
                    agencyMatch = true;
                }

                if (!agencyMatch) continue; // 如果机构不匹配，跳过该规则
            }

            // 2. 区域匹配逻辑
            if (!DEFAULT.equals(ruleDTO.getSkRegion())) {
                // 如果规则区域不是默认值，需要检查规则区域是否在数据区域的层级列表中
                boolean regionMatch = false;

                // 获取数据区域的层级列表
                ArrayList<String> regionHierarchy = region_hierarchy.get(shrAreaSplitDTO.getSkRegion());

                // 如果数据区域有层级列表，检查规则区域是否在其中
                if (regionHierarchy != null && regionHierarchy.contains(ruleDTO.getSkRegion())) {
                    regionMatch = true;
                }
                // 如果数据区域没有层级列表，只有直接匹配才算匹配成功
                else if (ruleDTO.getSkRegion().equals(shrAreaSplitDTO.getSkRegion())) {
                    regionMatch = true;
                }

                if (!regionMatch) continue; // 如果区域不匹配，跳过该规则
            }

            // 完整的匹配验证 - 与原始方法保持一致
            String dtoSkAccount = shrAreaSplitDTO.getSkAccount();
            String dtoSkAccountType = shrAreaSplitDTO.getSkAccountType();
            String dtoSkInvPty = shrAreaSplitDTO.getSkInvPty();
            String dtoSkInvPtyType = shrAreaSplitDTO.getSkInvPtyType();
            String dtoSkProduct = shrAreaSplitDTO.getSkProduct();
            String dtoSkProdType = shrAreaSplitDTO.getSkProdType();
            String dtoDkAgencyType = shrAreaSplitDTO.getDkAgencyType();
            String dtoDkCustType = shrAreaSplitDTO.getDkCustType();
            String dtoSkTradeAccoReg = shrAreaSplitDTO.getSkTradeAccoReg();
            String dtoAgencyNo = shrAreaSplitDTO.getAgencyNo();
            String dtoNetNo = shrAreaSplitDTO.getNetNo();
            String dtoDkShareType = shrAreaSplitDTO.getDkShareType();
            String dtoTrdShrTrxSerialNo = shrAreaSplitDTO.getTrdShrTrxSerialNo();
            String dtoDkTano = shrAreaSplitDTO.getDkTano();
            String dtoSkAccountOfAg = shrAreaSplitDTO.getSkAccountOfAg();
            String dtoDkBourseFlag = shrAreaSplitDTO.getDkBourseFlag();

            if (!DEFAULT.equals(ruleDTO.getSkAccount()) && !ruleDTO.getSkAccount().equals(dtoSkAccount)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkAccountType()) && !ruleDTO.getSkAccountType().equals(dtoSkAccountType)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkInvpty()) && !ruleDTO.getSkInvpty().equals(dtoSkInvPty)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkInvptyType()) && !ruleDTO.getSkInvptyType().equals(dtoSkInvPtyType)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkProduct()) && !ruleDTO.getSkProduct().equals(dtoSkProduct)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkProductType()) && !ruleDTO.getSkProductType().equals(dtoSkProdType)) continue;
            if (!WILDCARD.equals(ruleDTO.getDkAgencyType()) && !ruleDTO.getDkAgencyType().equals(dtoDkAgencyType)) continue;
            if (!WILDCARD.equals(ruleDTO.getDkCustType()) && !ruleDTO.getDkCustType().equals(dtoDkCustType)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkTradeaccoReg()) && !ruleDTO.getSkTradeaccoReg().equals(dtoSkTradeAccoReg)) continue;
            if (!WILDCARD.equals(ruleDTO.getAgencyno()) && !ruleDTO.getAgencyno().equals(dtoAgencyNo)) continue;
            if (!WILDCARD.equals(ruleDTO.getNetno()) && !ruleDTO.getNetno().equals(dtoNetNo)) continue;
            if (!WILDCARD.equals(ruleDTO.getDkShareType()) && !ruleDTO.getDkShareType().equals(dtoDkShareType)) continue;
            if (!WILDCARD.equals(ruleDTO.getCserialno()) && !ruleDTO.getCserialno().equals(dtoTrdShrTrxSerialNo)) continue;
            if (!WILDCARD.equals(ruleDTO.getDkTano()) && !ruleDTO.getDkTano().equals(dtoDkTano)) continue;
            if (!DEFAULT.equals(ruleDTO.getSkAccountOfAg()) && !ruleDTO.getSkAccountOfAg().equals(dtoSkAccountOfAg)) continue;
            if (!WILDCARD.equals(ruleDTO.getDkBourseflag()) && !ruleDTO.getDkBourseflag().equals(dtoDkBourseFlag)) continue;
            // 预先解析日期，避免重复解析
            int rule_from = Integer.parseInt(ruleDTO.getEffectiveFrom());
            int rule_to = Integer.parseInt(ruleDTO.getEffectiveTo());

            // 优化数据结构的创建和使用
            String[] current = new String[2];
            List<String[]> effective_data = new ArrayList<>(timeSlice.size() + 2);
            effective_data.addAll(timeSlice);
            List<String[]> currents = new ArrayList<>(4);

            // 使用局部变量缓存循环中的值
            for (String[] resource : timeSlice) {
                int data_from = Integer.parseInt(resource[0]);
                int data_to = Integer.parseInt(resource[1]);

                boolean flag = false;

                // 优化条件判断，减少重复计算
                if (rule_from <= data_from) {
                    if (data_from < rule_to) {
                        if (rule_to < data_to) {
                            // 情况1: rule_from <= data_from < rule_to < data_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(rule_to), Integer.toString(data_to)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        } else {
                            // 情况2: rule_from <= data_from < data_to <= rule_to
                            flag = true;
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                            if (!containsArray(currents, current)) {
                                currents.add(current);
                            }
                        }
                    }
                } else { // data_from < rule_from
                    if (rule_from < data_to) {
                        if (data_to <= rule_to) {
                            // 情况3: data_from < rule_from < data_to <= rule_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(data_from), Integer.toString(rule_from)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        } else { // rule_to < data_to
                            // 情况4: data_from < rule_from < rule_to < data_to
                            flag = true;
                            effective_data.add(new String[]{Integer.toString(data_from), Integer.toString(rule_from)});
                            effective_data.add(new String[]{Integer.toString(rule_to), Integer.toString(data_to)});
                            current = currentTimeSlice(data_from, data_to, rule_from, rule_to);
                        }
                    }
                }

                // 优化BigDecimal计算
                String ruleRatio = ruleDTO.getRatio();
                if (flag && ratio.subtract(new BigDecimal(ruleRatio)).compareTo(BigDecimal.ZERO) == 0) {
                    effective_data.remove(resource);
                }
            }

            // 更新timeSlice引用而不是创建新集合
            timeSlice = effective_data;

            // 优化BigDecimal计算
            ratio = ratio.subtract(new BigDecimal(ruleDTO.getRatio()));

            // 优化结果处理
            if (currents.size() > 1) {
                for (String[] curr : currents) {
                    fillShrAreaSplitResult(result, shrAreaSplitDTO, ruleDTO, curr);
                }
            } else {
                fillShrAreaSplitResult(result, shrAreaSplitDTO, ruleDTO, current);
            }

            // 提前返回，避免不必要的计算
            if (timeSlice.isEmpty()) {
                return result;
            }

            // 使用常量比较，避免创建新对象
            if (ratio.compareTo(BigDecimal.ZERO) == 0) {
                ratio = new BigDecimal("1.0000");
            }
        }

        return result;
    }

    // 已删除不再需要的方法，因为我们现在使用层级列表进行匹配

    /**
     * 测试层级关系匹配
     * 创建多个测试用例，测试不同层级的机构和区域匹配
     */
    public void testHierarchyMatching() {
        // 初始化数据
        initData();

        // 创建测试规则
        List<AreaSplitStaticRuleDTO> testRules = createTestRules();

        // 打印测试规则
        System.out.println("\n测试规则:");
        for (int i = 0; i < testRules.size(); i++) {
            AreaSplitStaticRuleDTO rule = testRules.get(i);
            System.out.println(i + ": 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() +
                              ", 目标机构=" + rule.getTargetAgency());
        }

        // 添加测试规则
        areaSplitStaticRule.clear();
        areaSplitStaticRule.addAll(testRules);

        // 重建决策树
        isTreeBuilt = false;
        buildDecisionTree();

        // 测试用例1: 直接匹配
        System.out.println("\n测试用例1: 直接匹配");
        ShrAreaSplitDTO testDTO1 = createTestDTO("BJ", "BJ");
        testMatchingCase(testDTO1);

        // 测试用例2: 上级机构匹配
        System.out.println("\n测试用例2: 上级机构匹配");
        ShrAreaSplitDTO testDTO2 = createTestDTO("NORTH", "NORTH_AREA");
        testMatchingCase(testDTO2);

        // 测试用例3: 下级机构匹配
        System.out.println("\n测试用例3: 下级机构匹配");
        ShrAreaSplitDTO testDTO3 = createTestDTO("BJ001", "DONGCHENG");
        testMatchingCase(testDTO3);

        // 测试用例4: 多级机构匹配
        System.out.println("\n测试用例4: 多级机构匹配");
        ShrAreaSplitDTO testDTO4 = createTestDTO("HQ", "CHINA");
        testMatchingCase(testDTO4);

        // 测试用例5: 复杂层级关系匹配
        System.out.println("\n测试用例5: 复杂层级关系匹配");
        ShrAreaSplitDTO testDTO5 = createTestDTO("GD", "GUANGDONG");
        testMatchingCase(testDTO5);
    }

    /**
     * 测试匹配用例
     */
    private void testMatchingCase(ShrAreaSplitDTO testDTO) {
        System.out.println("DTO: 机构=" + testDTO.getSkAgency() + ", 区域=" + testDTO.getSkRegion());

        // 执行原始方法
        ArrayList<ShrAreaSplitResult> results1 = getCurrentShareSplitResult(testDTO);
        System.out.println("原始方法结果数量: " + results1.size());

        // 执行决策树方法
        ArrayList<ShrAreaSplitResult> results2 = getCurrentShareSplitResultWithTree(testDTO);
        System.out.println("决策树方法结果数量: " + results2.size());

        // 打印结果
        if (!results1.isEmpty()) {
            System.out.println("原始方法匹配的目标机构: ");
            for (ShrAreaSplitResult result : results1) {
                System.out.println("  - " + result.getTargetAgency());
            }
        }

        if (!results2.isEmpty()) {
            System.out.println("决策树方法匹配的目标机构: ");
            for (ShrAreaSplitResult result : results2) {
                System.out.println("  - " + result.getTargetAgency());
            }
        }

        // 验证结果一致性
        validateResults(results1, results2);
    }

    /**
     * 创建测试规则
     */
    private List<AreaSplitStaticRuleDTO> createTestRules() {
        List<AreaSplitStaticRuleDTO> rules = new ArrayList<>();

        // 规则 1: 直接匹配 - 北京市东区
        AreaSplitStaticRuleDTO rule1 = new AreaSplitStaticRuleDTO();
        rule1.setSkAgency("BJ");
        rule1.setSkRegion("BJ");
        rule1.setSkAccount("-1");
        rule1.setSkAccountType("-1");
        rule1.setSkInvpty("-1");
        rule1.setSkInvptyType("-1");
        rule1.setSkProduct("-1");
        rule1.setSkProductType("-1");
        rule1.setDkAgencyType("*");
        rule1.setDkCustType("*");
        rule1.setSkTradeaccoReg("-1");
        rule1.setAgencyno("*");
        rule1.setNetno("*");
        rule1.setDkShareType("*");
        rule1.setCserialno("*");
        rule1.setDkTano("*");
        rule1.setSkAccountOfAg("-1");
        rule1.setDkBourseflag("*");
        rule1.setEffectiveFrom("********");
        rule1.setEffectiveTo("********");
        rule1.setRatio("1.0000");
        rule1.setTargetAgency("BJ001");
        rule1.setTargetRegion("BJ");
        rules.add(rule1);

        // 规则 2: 上级机构匹配 - 北部大区
        AreaSplitStaticRuleDTO rule2 = new AreaSplitStaticRuleDTO();
        rule2.setSkAgency("NORTH");
        rule2.setSkRegion("NORTH_AREA");
        rule2.setSkAccount("-1");
        rule2.setSkAccountType("-1");
        rule2.setSkInvpty("-1");
        rule2.setSkInvptyType("-1");
        rule2.setSkProduct("-1");
        rule2.setSkProductType("-1");
        rule2.setDkAgencyType("*");
        rule2.setDkCustType("*");
        rule2.setSkTradeaccoReg("-1");
        rule2.setAgencyno("*");
        rule2.setNetno("*");
        rule2.setDkShareType("*");
        rule2.setCserialno("*");
        rule2.setDkTano("*");
        rule2.setSkAccountOfAg("-1");
        rule2.setDkBourseflag("*");
        rule2.setEffectiveFrom("********");
        rule2.setEffectiveTo("********");
        rule2.setRatio("1.0000");
        rule2.setTargetAgency("NORTH001");
        rule2.setTargetRegion("NORTH");
        rules.add(rule2);

        // 规则 3: 下级机构匹配 - 北京市东城区
        AreaSplitStaticRuleDTO rule3 = new AreaSplitStaticRuleDTO();
        rule3.setSkAgency("BJ001");
        rule3.setSkRegion("DONGCHENG");
        rule3.setSkAccount("-1");
        rule3.setSkAccountType("-1");
        rule3.setSkInvpty("-1");
        rule3.setSkInvptyType("-1");
        rule3.setSkProduct("-1");
        rule3.setSkProductType("-1");
        rule3.setDkAgencyType("*");
        rule3.setDkCustType("*");
        rule3.setSkTradeaccoReg("-1");
        rule3.setAgencyno("*");
        rule3.setNetno("*");
        rule3.setDkShareType("*");
        rule3.setCserialno("*");
        rule3.setDkTano("*");
        rule3.setSkAccountOfAg("-1");
        rule3.setDkBourseflag("*");
        rule3.setEffectiveFrom("********");
        rule3.setEffectiveTo("********");
        rule3.setRatio("1.0000");
        rule3.setTargetAgency("BJ001001");
        rule3.setTargetRegion("DONGCHENG");
        rules.add(rule3);

        // 规则 4: 多级机构匹配 - 总部
        AreaSplitStaticRuleDTO rule4 = new AreaSplitStaticRuleDTO();
        rule4.setSkAgency("HQ");
        rule4.setSkRegion("CHINA");
        rule4.setSkAccount("-1");
        rule4.setSkAccountType("-1");
        rule4.setSkInvpty("-1");
        rule4.setSkInvptyType("-1");
        rule4.setSkProduct("-1");
        rule4.setSkProductType("-1");
        rule4.setDkAgencyType("*");
        rule4.setDkCustType("*");
        rule4.setSkTradeaccoReg("-1");
        rule4.setAgencyno("*");
        rule4.setNetno("*");
        rule4.setDkShareType("*");
        rule4.setCserialno("*");
        rule4.setDkTano("*");
        rule4.setSkAccountOfAg("-1");
        rule4.setDkBourseflag("*");
        rule4.setEffectiveFrom("********");
        rule4.setEffectiveTo("********");
        rule4.setRatio("1.0000");
        rule4.setTargetAgency("HQ001");
        rule4.setTargetRegion("CHINA");
        rules.add(rule4);

        // 规则 5: 复杂层级关系匹配 - 广东省
        AreaSplitStaticRuleDTO rule5 = new AreaSplitStaticRuleDTO();
        rule5.setSkAgency("GD");
        rule5.setSkRegion("GUANGDONG");
        rule5.setSkAccount("-1");
        rule5.setSkAccountType("-1");
        rule5.setSkInvpty("-1");
        rule5.setSkInvptyType("-1");
        rule5.setSkProduct("-1");
        rule5.setSkProductType("-1");
        rule5.setDkAgencyType("*");
        rule5.setDkCustType("*");
        rule5.setSkTradeaccoReg("-1");
        rule5.setAgencyno("*");
        rule5.setNetno("*");
        rule5.setDkShareType("*");
        rule5.setCserialno("*");
        rule5.setDkTano("*");
        rule5.setSkAccountOfAg("-1");
        rule5.setDkBourseflag("*");
        rule5.setEffectiveFrom("********");
        rule5.setEffectiveTo("********");
        rule5.setRatio("1.0000");
        rule5.setTargetAgency("GD001");
        rule5.setTargetRegion("GUANGDONG");
        rules.add(rule5);

        return rules;
    }

    /**
     * 创建测试DTO
     */
    private ShrAreaSplitDTO createTestDTO(String agency, String region) {
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency(agency);
        testDTO.setSkRegion(region);
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");
        return testDTO;
    }

    /**
     * 验证结果一致性
     */
    public void testResultConsistency() {
        // 初始化数据
        initData();

        // 创建测试数据
        ShrAreaSplitDTO testDTO = createTestDTO();

        // 执行原始方法
        System.out.println("\n执行原始方法...");
        ArrayList<ShrAreaSplitResult> results1 = getCurrentShareSplitResult(testDTO);

        // 执行决策树方法
        System.out.println("\n执行决策树方法...");
        ArrayList<ShrAreaSplitResult> results2 = getCurrentShareSplitResultWithTree(testDTO);

        // 验证结果
        validateResults(results1, results2);
    }

    /**
     * 创建测试DTO
     */
    private ShrAreaSplitDTO createTestDTO() {
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");
        return testDTO;
    }

    /**
     * 测试决策树优化后的性能
     * @param ruleCount 规则数量
     * @return 执行时间（毫秒）
     */
    public long testPerformanceWithTreeOptimization(int ruleCount) {
        // 清空现有规则
        areaSplitStaticRule.clear();
        isTreeBuilt = false;

        // 初始化层级关系
        initHierarchyData();

        // 生成随机规则
        System.out.println("生成" + ruleCount + "条随机规则...");
        long startGeneration = System.currentTimeMillis();
        for (int i = 0; i < ruleCount; i++) {
            areaSplitStaticRule.add(generateRandomRule());
        }
        long endGeneration = System.currentTimeMillis();
        System.out.println("规则生成完成，耗时: " + (endGeneration - startGeneration) + "ms");

        // 构建决策树
        buildDecisionTree();

        // 创建测试数据
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");

        // 执行拆分并计时
        System.out.println("开始执行getCurrentShareSplitResultWithTree方法...");
        long startTime = System.currentTimeMillis();
        ArrayList<ShrAreaSplitResult> results = getCurrentShareSplitResultWithTree(testDTO);
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        System.out.println("执行完成，耗时: " + executionTime + "ms");
        System.out.println("结果数量: " + results.size());

        return executionTime;
    }

    /**
     * 测试对比原始方法和决策树优化方法的性能
     * @param ruleCount 规则数量
     */
    public void testPerformanceComparison(int ruleCount) {
        // 清空现有规则
        areaSplitStaticRule.clear();
        isTreeBuilt = false;

        // 初始化层级关系
        initHierarchyData();

        // 生成随机规则
        System.out.println("生成" + ruleCount + "条随机规则...");
        long startGeneration = System.currentTimeMillis();
        for (int i = 0; i < ruleCount; i++) {
            areaSplitStaticRule.add(generateRandomRule());
        }
        long endGeneration = System.currentTimeMillis();
        System.out.println("规则生成完成，耗时: " + (endGeneration - startGeneration) + "ms");

        // 创建测试数据
        ShrAreaSplitDTO testDTO = new ShrAreaSplitDTO();
        testDTO.setSkAgency("BJ");
        testDTO.setSkRegion("BJ");
        testDTO.setSkAccount("ACC001");
        testDTO.setSkAccountType("1");
        testDTO.setSkInvPty("INV001");
        testDTO.setSkInvPtyType("1");
        testDTO.setSkProduct("PROD001");
        testDTO.setSkProdType("1");
        testDTO.setDkAgencyType("1");
        testDTO.setDkCustType("1");
        testDTO.setSkTradeAccoReg("REG001");
        testDTO.setAgencyNo("AG001");
        testDTO.setNetNo("NET001");
        testDTO.setDkShareType("1");
        testDTO.setTrdShrTrxSerialNo("TRX001");
        testDTO.setDkTano("TANO001");
        testDTO.setSkAccountOfAg("ACAG001");
        testDTO.setDkBourseFlag("1");
        testDTO.setEffectiveFrom("********");
        testDTO.setEffectiveTo("********");

        // 测试原始方法
        System.out.println("\n测试原始方法...");
        long startTime1 = System.currentTimeMillis();
        ArrayList<ShrAreaSplitResult> results1 = getCurrentShareSplitResult(testDTO);
        long endTime1 = System.currentTimeMillis();
        long executionTime1 = endTime1 - startTime1;

        System.out.println("原始方法执行完成，耗时: " + executionTime1 + "ms");
        System.out.println("结果数量: " + results1.size());

        // 构建决策树
        buildDecisionTree();

        // 测试决策树优化方法
        System.out.println("\n测试决策树优化方法...");
        long startTime2 = System.currentTimeMillis();
        ArrayList<ShrAreaSplitResult> results2 = getCurrentShareSplitResultWithTree(testDTO);
        long endTime2 = System.currentTimeMillis();
        long executionTime2 = endTime2 - startTime2;

        System.out.println("决策树优化方法执行完成，耗时: " + executionTime2 + "ms");
        System.out.println("结果数量: " + results2.size());

        // 计算性能提升
        double improvement = (double) executionTime1 / executionTime2;
        System.out.println("\n性能提升: " + String.format("%.2f", improvement) + "倍");
        System.out.println("原始方法耗时: " + executionTime1 + "ms");
        System.out.println("决策树优化方法耗时: " + executionTime2 + "ms");
        System.out.println("节省时间: " + (executionTime1 - executionTime2) + "ms");

        // 验证结果是否一致
        validateResults(results1, results2);
    }

    /**
     * 验证两种方法的结果是否一致
     * @param originalResults 原始方法的结果
     * @param treeResults 决策树方法的结果
     */
    private void validateResults(List<ShrAreaSplitResult> originalResults, List<ShrAreaSplitResult> treeResults) {
        // 比较结果数量
        System.out.println("\n结果验证:");
        System.out.println("原始方法结果数量: " + originalResults.size());
        System.out.println("决策树方法结果数量: " + treeResults.size());

        // 详细比较结果内容
        if (originalResults.size() != treeResults.size()) {
            System.out.println("结果数量不一致，详细比较:");

            // 打印原始结果
            System.out.println("\n原始方法结果:");
            for (int i = 0; i < originalResults.size(); i++) {
                ShrAreaSplitResult r = originalResults.get(i);
                System.out.println(i + ": " + r.getEffectiveFrom() + "-" + r.getEffectiveTo() +
                                  ", 目标机构: " + r.getTargetAgency() + ", 比例: " + r.getRatio());
            }

            // 打印决策树结果
            System.out.println("\n决策树方法结果:");
            for (int i = 0; i < treeResults.size(); i++) {
                ShrAreaSplitResult r = treeResults.get(i);
                System.out.println(i + ": " + r.getEffectiveFrom() + "-" + r.getEffectiveTo() +
                                  ", 目标机构: " + r.getTargetAgency() + ", 比例: " + r.getRatio());
            }
        } else {
            System.out.println("结果数量一致，进一步比较内容...");
            boolean contentMatch = true;

            for (int i = 0; i < originalResults.size(); i++) {
                ShrAreaSplitResult or = originalResults.get(i);
                ShrAreaSplitResult tr = treeResults.get(i);

                if (!or.getEffectiveFrom().equals(tr.getEffectiveFrom()) ||
                    !or.getEffectiveTo().equals(tr.getEffectiveTo()) ||
                    !or.getTargetAgency().equals(tr.getTargetAgency()) ||
                    !or.getRatio().equals(tr.getRatio())) {

                    contentMatch = false;
                    System.out.println("结果 " + i + " 不匹配:");
                    System.out.println("原始: " + or.getEffectiveFrom() + "-" + or.getEffectiveTo() +
                                      ", 目标机构: " + or.getTargetAgency() + ", 比例: " + or.getRatio());
                    System.out.println("决策树: " + tr.getEffectiveFrom() + "-" + tr.getEffectiveTo() +
                                      ", 目标机构: " + tr.getTargetAgency() + ", 比例: " + tr.getRatio());
                }
            }

            if (contentMatch) {
                System.out.println("所有结果内容完全匹配!");
            }
        }
    }
}
