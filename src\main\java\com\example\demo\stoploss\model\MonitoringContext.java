package com.example.demo.stoploss.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 监控上下文
 * 包含监控过程中的状态信息和临时数据
 */
@Data
public class MonitoringContext {
    
    /**
     * 上下文ID
     */
    private String contextId;
    
    /**
     * 组合信息
     */
    private PensionPortfolio portfolio;
    
    /**
     * 当前规则
     */
    private StopLossRule currentRule;
    
    /**
     * 监控开始时间
     */
    private LocalDateTime monitoringStartTime;
    
    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;
    
    /**
     * 条件评估结果缓存
     */
    private Map<String, Boolean> conditionResults;
    
    /**
     * 事件执行历史
     */
    private Map<String, LocalDateTime> eventExecutionHistory;
    
    /**
     * 临时数据存储
     */
    private Map<String, Object> temporaryData;
    
    /**
     * 监控状态
     */
    private MonitoringStatus status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 监控状态枚举
     */
    public enum MonitoringStatus {
        ACTIVE("监控中"),
        PAUSED("暂停"),
        STOPPED("已停止"),
        ERROR("错误");
        
        private final String description;
        
        MonitoringStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数
     */
    public MonitoringContext(String contextId, PensionPortfolio portfolio) {
        this.contextId = contextId;
        this.portfolio = portfolio;
        this.monitoringStartTime = LocalDateTime.now();
        this.lastCheckTime = LocalDateTime.now();
        this.conditionResults = new ConcurrentHashMap<>();
        this.eventExecutionHistory = new ConcurrentHashMap<>();
        this.temporaryData = new ConcurrentHashMap<>();
        this.status = MonitoringStatus.ACTIVE;
    }
    
    /**
     * 设置条件评估结果
     */
    public void setConditionResult(String conditionId, boolean result) {
        conditionResults.put(conditionId, result);
    }
    
    /**
     * 获取条件评估结果
     */
    public Boolean getConditionResult(String conditionId) {
        return conditionResults.get(conditionId);
    }
    
    /**
     * 记录事件执行时间
     */
    public void recordEventExecution(String eventId) {
        eventExecutionHistory.put(eventId, LocalDateTime.now());
    }
    
    /**
     * 获取事件最后执行时间
     */
    public LocalDateTime getEventLastExecutionTime(String eventId) {
        return eventExecutionHistory.get(eventId);
    }
    
    /**
     * 检查事件是否已执行
     */
    public boolean isEventExecuted(String eventId) {
        return eventExecutionHistory.containsKey(eventId);
    }
    
    /**
     * 设置临时数据
     */
    public void setTemporaryData(String key, Object value) {
        temporaryData.put(key, value);
    }
    
    /**
     * 获取临时数据
     */
    public Object getTemporaryData(String key) {
        return temporaryData.get(key);
    }
    
    /**
     * 获取临时数据（指定类型）
     */
    @SuppressWarnings("unchecked")
    public <T> T getTemporaryData(String key, Class<T> type) {
        Object value = temporaryData.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 清除条件结果缓存
     */
    public void clearConditionResults() {
        conditionResults.clear();
    }
    
    /**
     * 清除事件执行历史
     */
    public void clearEventExecutionHistory() {
        eventExecutionHistory.clear();
    }
    
    /**
     * 清除临时数据
     */
    public void clearTemporaryData() {
        temporaryData.clear();
    }
    
    /**
     * 重置上下文
     */
    public void reset() {
        clearConditionResults();
        clearEventExecutionHistory();
        clearTemporaryData();
        this.status = MonitoringStatus.ACTIVE;
        this.errorMessage = null;
        this.lastCheckTime = LocalDateTime.now();
    }
    
    /**
     * 更新最后检查时间
     */
    public void updateLastCheckTime() {
        this.lastCheckTime = LocalDateTime.now();
    }
    
    /**
     * 设置错误状态
     */
    public void setError(String errorMessage) {
        this.status = MonitoringStatus.ERROR;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 检查是否处于活跃状态
     */
    public boolean isActive() {
        return status == MonitoringStatus.ACTIVE;
    }
    
    /**
     * 检查是否处于错误状态
     */
    public boolean isError() {
        return status == MonitoringStatus.ERROR;
    }
}
