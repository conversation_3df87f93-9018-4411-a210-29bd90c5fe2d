package com.example.demo.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 层级关系工具类
 * 用于处理机构和区域的层级关系
 */
public class HierarchyUtil {

    /**
     * 获取指定机构的所有上级机构
     * @param agency 机构代码
     * @param hierarchy 机构层级关系
     * @return 所有上级机构列表（包括自身）
     */
    public static List<String> getAllParentAgencies(String agency, Map<String, ArrayList<String>> hierarchy) {
        List<String> result = new ArrayList<>();
        result.add(agency); // 添加自身
        
        // 构建反向映射，子机构 -> 父机构
        Map<String, String> childToParent = buildChildToParentMap(hierarchy);
        
        // 递归查找所有上级
        String current = agency;
        while (childToParent.containsKey(current)) {
            String parent = childToParent.get(current);
            result.add(parent);
            current = parent;
        }
        
        return result;
    }
    
    /**
     * 获取指定机构的所有下级机构
     * @param agency 机构代码
     * @param hierarchy 机构层级关系
     * @return 所有下级机构列表（不包括自身）
     */
    public static List<String> getAllSubAgencies(String agency, Map<String, ArrayList<String>> hierarchy) {
        List<String> result = new ArrayList<>();
        collectSubAgencies(agency, hierarchy, result);
        return result;
    }
    
    /**
     * 递归收集下级机构
     */
    private static void collectSubAgencies(String agency, Map<String, ArrayList<String>> hierarchy, List<String> result) {
        ArrayList<String> directSubs = hierarchy.get(agency);
        if (directSubs != null) {
            result.addAll(directSubs);
            for (String sub : directSubs) {
                collectSubAgencies(sub, hierarchy, result);
            }
        }
    }
    
    /**
     * 构建子机构到父机构的映射
     */
    private static Map<String, String> buildChildToParentMap(Map<String, ArrayList<String>> hierarchy) {
        Map<String, String> childToParent = new HashMap<>();
        
        for (Map.Entry<String, ArrayList<String>> entry : hierarchy.entrySet()) {
            String parent = entry.getKey();
            ArrayList<String> children = entry.getValue();
            
            if (children != null) {
                for (String child : children) {
                    childToParent.put(child, parent);
                }
            }
        }
        
        return childToParent;
    }
    
    /**
     * 检查规则机构是否匹配数据机构
     * 规则适用于上层，数据来自下层
     * 如果数据机构是规则机构自身或其下级机构，则匹配成功
     * 
     * @param ruleAgency 规则机构
     * @param dtoAgency 数据机构
     * @param hierarchy 机构层级关系
     * @return 是否匹配
     */
    public static boolean isAgencyMatch(String ruleAgency, String dtoAgency, Map<String, ArrayList<String>> hierarchy) {
        // 如果规则机构是默认值，则匹配所有机构
        if ("-1".equals(ruleAgency)) {
            return true;
        }
        
        // 如果规则机构和数据机构相同，则匹配成功
        if (ruleAgency.equals(dtoAgency)) {
            return true;
        }
        
        // 检查数据机构是否是规则机构的下级
        List<String> subAgencies = getAllSubAgencies(ruleAgency, hierarchy);
        return subAgencies.contains(dtoAgency);
    }
    
    /**
     * 检查规则区域是否匹配数据区域
     * 规则适用于上层，数据来自下层
     * 如果数据区域是规则区域自身或其下级区域，则匹配成功
     * 
     * @param ruleRegion 规则区域
     * @param dtoRegion 数据区域
     * @param hierarchy 区域层级关系
     * @return 是否匹配
     */
    public static boolean isRegionMatch(String ruleRegion, String dtoRegion, Map<String, ArrayList<String>> hierarchy) {
        // 如果规则区域是默认值，则匹配所有区域
        if ("-1".equals(ruleRegion)) {
            return true;
        }
        
        // 如果规则区域和数据区域相同，则匹配成功
        if (ruleRegion.equals(dtoRegion)) {
            return true;
        }
        
        // 检查数据区域是否是规则区域的下级
        List<String> subRegions = getAllSubAgencies(ruleRegion, hierarchy);
        return subRegions.contains(dtoRegion);
    }
}
