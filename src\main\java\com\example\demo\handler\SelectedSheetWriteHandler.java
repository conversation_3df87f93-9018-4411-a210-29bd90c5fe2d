package com.example.demo.handler;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.example.demo.annotation.ExcelSelected;
import com.example.demo.model.SelectOption;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

public class SelectedSheetWriteHandler implements SheetWriteHandler {
    
    private final Map<String, List<SelectOption>> optionsMap;
    
    public SelectedSheetWriteHandler(Map<String, List<SelectOption>> optionsMap) {
        this.optionsMap = optionsMap;
    }
    
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();
        
        for (Head head : writeSheetHolder.getExcelWriteHeadProperty().getHeadMap().values()) {
            Field field = head.getField();
            ExcelSelected annotation = field.getAnnotation(ExcelSelected.class);
            if (annotation != null) {
                List<SelectOption> options = optionsMap.get(annotation.value());
                if (options != null) {
                    String[] values = options.stream().map(SelectOption::getValue).toArray(String[]::new);
                    DataValidationConstraint constraint = helper.createExplicitListConstraint(values);
                    int colIndex = head.getColumnIndex();
                    CellRangeAddressList range = new CellRangeAddressList(1, 65536, colIndex, colIndex);
                    sheet.addValidationData(helper.createValidation(constraint, range));
                }
            }
        }
    }
}
