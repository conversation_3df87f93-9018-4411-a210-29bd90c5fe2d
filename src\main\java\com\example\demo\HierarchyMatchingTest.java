package com.example.demo;

import com.example.demo.data.HierarchyData;
import com.example.demo.data.TestData;
import com.example.demo.model.AreaSplitStaticRuleDTO;
import com.example.demo.model.ShrAreaSplitDTO;
import com.example.demo.util.HierarchyMatcher;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 层级关系匹配测试类
 */
public class HierarchyMatchingTest {

    public static void main(String[] args) {
        System.out.println("开始测试层级关系匹配...");
        
        // 初始化层级关系
        Map<String, ArrayList<String>> agencyHierarchy = HierarchyData.initAgencyHierarchy();
        Map<String, ArrayList<String>> regionHierarchy = HierarchyData.initRegionHierarchy();
        
        // 创建测试规则
        List<AreaSplitStaticRuleDTO> rules = TestData.createTestRules();
        
        // 创建测试数据
        List<ShrAreaSplitDTO> dtos = TestData.createTestDTOs();
        
        // 打印层级关系
        printHierarchy("机构层级关系", agencyHierarchy);
        printHierarchy("区域层级关系", regionHierarchy);
        
        // 打印测试规则
        System.out.println("\n测试规则:");
        for (int i = 0; i < rules.size(); i++) {
            AreaSplitStaticRuleDTO rule = rules.get(i);
            System.out.println(i + ": 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                              ", 目标机构=" + rule.getTargetAgency());
        }
        
        // 打印测试数据
        System.out.println("\n测试数据:");
        for (int i = 0; i < dtos.size(); i++) {
            ShrAreaSplitDTO dto = dtos.get(i);
            System.out.println(i + ": 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
        }
        
        // 测试匹配
        System.out.println("\n测试匹配结果:");
        for (ShrAreaSplitDTO dto : dtos) {
            System.out.println("\n数据: 机构=" + dto.getSkAgency() + ", 区域=" + dto.getSkRegion());
            
            for (AreaSplitStaticRuleDTO rule : rules) {
                boolean agencyMatch = HierarchyMatcher.isAgencyMatch(rule.getSkAgency(), dto.getSkAgency(), agencyHierarchy);
                boolean regionMatch = HierarchyMatcher.isRegionMatch(rule.getSkRegion(), dto.getSkRegion(), regionHierarchy);
                boolean match = agencyMatch && regionMatch;
                
                System.out.println("规则: 机构=" + rule.getSkAgency() + ", 区域=" + rule.getSkRegion() + 
                                  ", 目标机构=" + rule.getTargetAgency() + 
                                  " => 匹配结果: " + (match ? "匹配" : "不匹配") + 
                                  " (机构匹配: " + agencyMatch + ", 区域匹配: " + regionMatch + ")");
            }
        }
        
        System.out.println("\n层级关系匹配测试完成");
    }
    
    /**
     * 打印层级关系
     */
    private static void printHierarchy(String title, Map<String, ArrayList<String>> hierarchy) {
        System.out.println("\n" + title + ":");
        for (Map.Entry<String, ArrayList<String>> entry : hierarchy.entrySet()) {
            System.out.println(entry.getKey() + " -> " + entry.getValue());
        }
    }
}
